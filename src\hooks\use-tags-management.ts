'use client';

import { useState, useEffect, useCallback } from 'react';
import { getTags, createTag, updateTag, deleteTag } from '@/lib/database-helpers';
import type { Tag, InsertTag, UpdateTag } from '@/types';

interface TagWithStats extends Tag {
  post_count?: number;
  usage_trend?: 'up' | 'down' | 'stable';
}

interface TagFilters {
  search: string;
  usageMin: string;
  usageMax: string;
  hasUsage: string;
  dateFrom: string;
  dateTo: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface UseTagsManagementReturn {
  tags: TagWithStats[];
  filteredTags: TagWithStats[];
  loading: {
    tags: boolean;
    actions: boolean;
  };
  errors: {
    tags: string | null;
    actions: string | null;
  };
  filters: TagFilters;
  updateFilters: (newFilters: Partial<TagFilters>) => void;
  resetFilters: () => void;
  refresh: () => Promise<void>;
  createNewTag: (tagData: InsertTag) => Promise<{ success: boolean; data?: Tag; error?: string }>;
  updateExistingTag: (id: string, tagData: UpdateTag) => Promise<{ success: boolean; data?: Tag; error?: string }>;
  deleteTags: (tags: TagWithStats[]) => Promise<void>;
  duplicateTags: (tags: TagWithStats[]) => Promise<void>;
  mergeTags: (targetTag: TagWithStats, tagsToMerge: TagWithStats[]) => Promise<void>;
  exportTags: (tags: TagWithStats[]) => Promise<void>;
}

const defaultFilters: TagFilters = {
  search: '',
  usageMin: '',
  usageMax: '',
  hasUsage: '',
  dateFrom: '',
  dateTo: '',
  sortBy: 'name',
  sortOrder: 'asc',
};

export function useTagsManagement(): UseTagsManagementReturn {
  const [tags, setTags] = useState<TagWithStats[]>([]);
  const [loading, setLoading] = useState({
    tags: true,
    actions: false,
  });
  const [errors, setErrors] = useState({
    tags: null as string | null,
    actions: null as string | null,
  });
  const [filters, setFilters] = useState<TagFilters>(defaultFilters);

  // Fetch tags
  const fetchTags = useCallback(async () => {
    setLoading(prev => ({ ...prev, tags: true }));
    setErrors(prev => ({ ...prev, tags: null }));

    try {
      const { data, error } = await getTags();
      if (error) {
        throw new Error(error);
      }
      
      // Add mock stats for now - in real implementation, this would come from the database
      const tagsWithStats: TagWithStats[] = (data || []).map(tag => ({
        ...tag,
        post_count: Math.floor(Math.random() * 50), // Mock data
        usage_trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as 'up' | 'down' | 'stable',
      }));
      
      setTags(tagsWithStats);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch tags';
      setErrors(prev => ({ ...prev, tags: errorMessage }));
      console.error('Error fetching tags:', error);
    } finally {
      setLoading(prev => ({ ...prev, tags: false }));
    }
  }, []);

  // Filter and sort tags
  const filteredTags = useCallback(() => {
    let filtered = [...tags];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(tag =>
        tag.name.toLowerCase().includes(searchLower) ||
        tag.slug.toLowerCase().includes(searchLower)
      );
    }

    // Apply usage filters
    if (filters.usageMin) {
      const min = parseInt(filters.usageMin);
      filtered = filtered.filter(tag => (tag.post_count || 0) >= min);
    }
    if (filters.usageMax) {
      const max = parseInt(filters.usageMax);
      filtered = filtered.filter(tag => (tag.post_count || 0) <= max);
    }

    // Apply has usage filter
    if (filters.hasUsage === 'true') {
      filtered = filtered.filter(tag => (tag.post_count || 0) > 0);
    } else if (filters.hasUsage === 'false') {
      filtered = filtered.filter(tag => (tag.post_count || 0) === 0);
    }

    // Apply date filters
    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(tag => new Date(tag.created_at || '') >= fromDate);
    }
    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(tag => new Date(tag.created_at || '') <= toDate);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[filters.sortBy as keyof TagWithStats];
      let bValue: any = b[filters.sortBy as keyof TagWithStats];

      // Handle different data types
      if (filters.sortBy === 'post_count') {
        aValue = aValue || 0;
        bValue = bValue || 0;
      } else if (filters.sortBy === 'created_at' || filters.sortBy === 'updated_at') {
        aValue = new Date(aValue || '');
        bValue = new Date(bValue || '');
      } else {
        aValue = String(aValue || '').toLowerCase();
        bValue = String(bValue || '').toLowerCase();
      }

      if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [tags, filters]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<TagFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  // Create tag
  const createNewTag = useCallback(async (tagData: InsertTag) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      const { data, error } = await createTag(tagData);
      if (error) {
        throw new Error(error);
      }

      // Refresh tags list
      await fetchTags();
      
      return { success: true, data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create tag';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      return { success: false, error: errorMessage };
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchTags]);

  // Update tag
  const updateExistingTag = useCallback(async (id: string, tagData: UpdateTag) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      const { data, error } = await updateTag(id, tagData);
      if (error) {
        throw new Error(error);
      }

      // Refresh tags list
      await fetchTags();
      
      return { success: true, data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update tag';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      return { success: false, error: errorMessage };
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchTags]);

  // Delete tags
  const deleteTags = useCallback(async (tagsToDelete: TagWithStats[]) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      for (const tag of tagsToDelete) {
        const { error } = await deleteTag(tag.id);
        if (error) {
          throw new Error(`Failed to delete ${tag.name}: ${error}`);
        }
      }

      // Refresh tags list
      await fetchTags();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete tags';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchTags]);

  // Duplicate tags
  const duplicateTags = useCallback(async (tagsToDuplicate: TagWithStats[]) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      for (const tag of tagsToDuplicate) {
        const duplicateData: InsertTag = {
          name: `${tag.name} (Copy)`,
          slug: `${tag.slug}-copy-${Date.now()}`,
        };

        const { error } = await createTag(duplicateData);
        if (error) {
          throw new Error(`Failed to duplicate ${tag.name}: ${error}`);
        }
      }

      // Refresh tags list
      await fetchTags();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to duplicate tags';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchTags]);

  // Merge tags
  const mergeTags = useCallback(async (targetTag: TagWithStats, tagsToMerge: TagWithStats[]) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      // In a real implementation, this would:
      // 1. Update all posts that use the merged tags to use the target tag
      // 2. Delete the merged tags
      // For now, we'll just delete the merged tags
      
      for (const tag of tagsToMerge) {
        if (tag.id !== targetTag.id) {
          const { error } = await deleteTag(tag.id);
          if (error) {
            throw new Error(`Failed to merge ${tag.name}: ${error}`);
          }
        }
      }

      // Refresh tags list
      await fetchTags();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to merge tags';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchTags]);

  // Export tags
  const exportTags = useCallback(async (tagsToExport: TagWithStats[]) => {
    try {
      const exportData = tagsToExport.map(tag => ({
        name: tag.name,
        slug: tag.slug,
        post_count: tag.post_count || 0,
        usage_trend: tag.usage_trend,
        created_at: tag.created_at,
        updated_at: tag.updated_at,
      }));

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `tags-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to export tags';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      throw error;
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  return {
    tags,
    filteredTags: filteredTags(),
    loading,
    errors,
    filters,
    updateFilters,
    resetFilters,
    refresh: fetchTags,
    createNewTag,
    updateExistingTag,
    deleteTags,
    duplicateTags,
    mergeTags,
    exportTags,
  };
}
