'use client';

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X } from 'lucide-react';
import type { Tag } from '@/types';

interface TagFilters {
  search: string;
  usageMin: string;
  usageMax: string;
  hasUsage: string;
  dateFrom: string;
  dateTo: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface TagsFiltersProps {
  filters: TagFilters;
  onFiltersChange: (filters: Partial<TagFilters>) => void;
  onReset: () => void;
  tags?: Tag[];
  loading?: boolean;
}

export function TagsFilters({
  filters,
  onFiltersChange,
  onReset,
  tags = [],
  loading = false,
}: TagsFiltersProps) {
  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'sortBy' || key === 'sortOrder') return false;
    return value && value !== 'all' && value !== '';
  }).length;

  const handleFilterChange = (key: keyof TagFilters, value: string) => {
    onFiltersChange({ [key]: value });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onReset}
              className="h-8 px-2"
            >
              <X className="h-3 w-3 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tags..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-9"
            disabled={loading}
          />
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={filters.hasUsage === 'true' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFilterChange('hasUsage', 
              filters.hasUsage === 'true' ? '' : 'true'
            )}
          >
            Used Tags
          </Button>
          <Button
            variant={filters.hasUsage === 'false' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFilterChange('hasUsage', 
              filters.hasUsage === 'false' ? '' : 'false'
            )}
          >
            Unused Tags
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
