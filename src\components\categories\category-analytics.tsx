'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  <PERSON><PERSON><PERSON>3,
  <PERSON><PERSON>dingUp,
  FileText,
  Folder,
  Users,
  Calendar,
} from 'lucide-react';
import type { Category } from '@/types';

interface CategoryAnalyticsProps {
  categories: Category[];
}

export function CategoryAnalytics({ categories }: CategoryAnalyticsProps) {
  // Calculate analytics
  const totalCategories = categories.length;
  const totalPosts = categories.reduce((sum, cat) => sum + (cat.post_count || 0), 0);
  const topLevelCategories = categories.filter(cat => !cat.parent_id).length;
  const subcategories = categories.filter(cat => cat.parent_id).length;
  
  // Most used categories
  const mostUsedCategories = categories
    .filter(cat => (cat.post_count || 0) > 0)
    .sort((a, b) => (b.post_count || 0) - (a.post_count || 0))
    .slice(0, 5);

  // Unused categories
  const unusedCategories = categories.filter(cat => (cat.post_count || 0) === 0);

  // Categories with descriptions
  const categoriesWithDescriptions = categories.filter(cat => cat.description?.trim());

  // Recent categories (created in last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
  const recentCategories = categories.filter(cat => 
    new Date(cat.created_at) > thirtyDaysAgo
  );

  const maxPostCount = Math.max(...categories.map(cat => cat.post_count || 0), 1);

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Folder className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Categories</p>
                <p className="text-2xl font-bold">{totalCategories}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <FileText className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Posts</p>
                <p className="text-2xl font-bold">{totalPosts}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BarChart3 className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Avg Posts/Category</p>
                <p className="text-2xl font-bold">
                  {totalCategories > 0 ? Math.round(totalPosts / totalCategories) : 0}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <Calendar className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Recent (30d)</p>
                <p className="text-2xl font-bold">{recentCategories.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Category Structure */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Folder className="h-5 w-5" />
              Category Structure
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">Top-level categories</span>
              <Badge variant="secondary">{topLevelCategories}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Subcategories</span>
              <Badge variant="secondary">{subcategories}</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">With descriptions</span>
              <Badge variant="secondary">
                {categoriesWithDescriptions.length} 
                ({Math.round((categoriesWithDescriptions.length / totalCategories) * 100)}%)
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Unused categories</span>
              <Badge variant={unusedCategories.length > 0 ? "destructive" : "secondary"}>
                {unusedCategories.length}
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Most Used Categories */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Most Used Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            {mostUsedCategories.length > 0 ? (
              <div className="space-y-3">
                {mostUsedCategories.map((category, index) => (
                  <div key={category.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">#{index + 1}</span>
                        <span className="text-sm truncate">{category.name}</span>
                      </div>
                      <Badge variant="secondary">
                        {category.post_count} posts
                      </Badge>
                    </div>
                    <Progress 
                      value={(category.post_count || 0) / maxPostCount * 100} 
                      className="h-2"
                    />
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground text-center py-4">
                No categories with posts yet
              </p>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Unused Categories Warning */}
      {unusedCategories.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <FileText className="h-5 w-5" />
              Unused Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-yellow-700 mb-3">
              You have {unusedCategories.length} categories that aren't being used by any posts. 
              Consider removing them or creating content for these categories.
            </p>
            <div className="flex flex-wrap gap-2">
              {unusedCategories.slice(0, 10).map((category) => (
                <Badge key={category.id} variant="outline" className="text-yellow-700">
                  {category.name}
                </Badge>
              ))}
              {unusedCategories.length > 10 && (
                <Badge variant="outline" className="text-yellow-700">
                  +{unusedCategories.length - 10} more
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Activity */}
      {recentCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Recent Categories (Last 30 Days)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {recentCategories.slice(0, 5).map((category) => (
                <div key={category.id} className="flex items-center justify-between py-2">
                  <div>
                    <span className="font-medium">{category.name}</span>
                    {category.description && (
                      <p className="text-sm text-muted-foreground truncate">
                        {category.description}
                      </p>
                    )}
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary">
                      {category.post_count || 0} posts
                    </Badge>
                    <p className="text-xs text-muted-foreground mt-1">
                      {new Date(category.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
