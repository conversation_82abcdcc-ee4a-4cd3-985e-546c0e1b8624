-- Migration: Add category hierarchy support
-- This adds parent_id and meta fields to the categories table

-- Add parent_id column for category hierarchy
ALTER TABLE categories 
ADD COLUMN parent_id UUID REFERENCES categories(id) ON DELETE SET NULL;

-- Add SEO meta fields
ALTER TABLE categories 
ADD COLUMN meta_title TEXT,
ADD COLUMN meta_description TEXT;

-- Add index for parent_id lookups
CREATE INDEX idx_categories_parent_id ON categories(parent_id) WHERE deleted_at IS NULL;

-- Add constraint to prevent circular references
CREATE OR REPLACE FUNCTION check_category_hierarchy()
RETURNS TRIGGER AS $$
DECLARE
  current_id UUID;
  depth INTEGER := 0;
  max_depth INTEGER := 10; -- Prevent infinite loops
BEGIN
  -- If parent_id is NULL, no check needed
  IF NEW.parent_id IS NULL THEN
    RETURN NEW;
  END IF;
  
  -- Check if trying to set self as parent
  IF NEW.id = NEW.parent_id THEN
    RAISE EXCEPTION 'Category cannot be its own parent';
  END IF;
  
  -- Check for circular reference by traversing up the hierarchy
  current_id := NEW.parent_id;
  
  WHILE current_id IS NOT NULL AND depth < max_depth LOOP
    -- If we find our own ID in the parent chain, it's circular
    IF current_id = NEW.id THEN
      RAISE EXCEPTION 'Circular reference detected in category hierarchy';
    END IF;
    
    -- Get the parent of the current category
    SELECT parent_id INTO current_id 
    FROM categories 
    WHERE id = current_id AND deleted_at IS NULL;
    
    depth := depth + 1;
  END LOOP;
  
  -- If we hit max depth, something is wrong
  IF depth >= max_depth THEN
    RAISE EXCEPTION 'Category hierarchy too deep (max % levels)', max_depth;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to check hierarchy on insert/update
CREATE TRIGGER check_category_hierarchy_trigger
  BEFORE INSERT OR UPDATE ON categories
  FOR EACH ROW
  EXECUTE FUNCTION check_category_hierarchy();

-- Add comments
COMMENT ON COLUMN categories.parent_id IS 'Parent category for hierarchical organization';
COMMENT ON COLUMN categories.meta_title IS 'SEO meta title for category pages';
COMMENT ON COLUMN categories.meta_description IS 'SEO meta description for category pages';

-- Create a view for categories with post counts
CREATE OR REPLACE VIEW categories_with_stats AS
SELECT 
  c.*,
  COALESCE(post_counts.post_count, 0) as post_count
FROM categories c
LEFT JOIN (
  SELECT 
    category_id,
    COUNT(*) as post_count
  FROM posts 
  WHERE deleted_at IS NULL 
    AND status = 'published'
  GROUP BY category_id
) post_counts ON c.id = post_counts.category_id
WHERE c.deleted_at IS NULL;

-- Grant permissions on the view
GRANT SELECT ON categories_with_stats TO authenticated;

-- Update RLS policy for the view
CREATE POLICY "categories_with_stats_select_policy" ON categories_with_stats
  FOR SELECT
  USING (auth.uid() IS NOT NULL);
