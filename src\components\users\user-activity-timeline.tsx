'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Activity, 
  RefreshCw, 
  Clock, 
  User, 
  FileText, 
  Settings, 
  Shield,
  Mail,
  Edit,
  Trash2,
  Plus,
  Eye
} from 'lucide-react';
import { getUserActivity } from '@/lib/database-helpers';
import type { AuditLog, AuditAction } from '@/types';

interface UserActivityTimelineProps {
  userId?: string;
  limit?: number;
  className?: string;
}

const actionIcons: Record<AuditAction, React.ReactNode> = {
  CREATE: <Plus className="h-3 w-3" />,
  UPDATE: <Edit className="h-3 w-3" />,
  DELETE: <Trash2 className="h-3 w-3" />,
  LOGIN: <User className="h-3 w-3" />,
  LOGOUT: <User className="h-3 w-3" />,
  LOGIN_FAIL: <Shield className="h-3 w-3" />,
  INVITE_SENT: <Mail className="h-3 w-3" />,
  INVITE_ACCEPTED: <Mail className="h-3 w-3" />,
  ROLE_CHANGED: <Shield className="h-3 w-3" />,
  PASSWORD_CHANGED: <Settings className="h-3 w-3" />,
  VIEW: <Eye className="h-3 w-3" />,
};

const actionColors: Record<AuditAction, string> = {
  CREATE: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  UPDATE: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  DELETE: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  LOGIN: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  LOGOUT: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
  LOGIN_FAIL: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  INVITE_SENT: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  INVITE_ACCEPTED: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  ROLE_CHANGED: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300',
  PASSWORD_CHANGED: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300',
  VIEW: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
};

export function UserActivityTimeline({
  userId,
  limit = 20,
  className,
}: UserActivityTimelineProps) {
  const [activities, setActivities] = useState<AuditLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchActivity = async () => {
    setLoading(true);
    setError(null);

    try {
      const response = await getUserActivity(userId, {
        page: 1,
        limit,
        sortBy: 'created_at',
        sortOrder: 'desc',
      });

      if (response.error) {
        setError(response.error);
        return;
      }

      setActivities(response.data || []);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch activity');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchActivity();
  }, [userId, limit]);

  const formatDate = (date: Date | string) => {
    const d = new Date(date);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - d.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    if (diffInMinutes < 10080) return `${Math.floor(diffInMinutes / 1440)}d ago`;
    
    return d.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: d.getFullYear() !== now.getFullYear() ? 'numeric' : undefined,
    });
  };

  const getActivityDescription = (activity: AuditLog) => {
    const { action, target_table_name, description } = activity;
    
    if (description) return description;
    
    const table = target_table_name || 'item';
    const actionText = action.toLowerCase().replace('_', ' ');
    
    return `${actionText} ${table}`;
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Activity Timeline
          </CardTitle>
          <CardDescription>
            {userId ? 'User activity history' : 'Recent system activity'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-start space-x-3">
                <div className="h-8 w-8 rounded-full bg-gray-200 animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 w-3/4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 w-1/2 bg-gray-200 rounded animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Activity Timeline
            </CardTitle>
            <CardDescription>
              {userId ? 'User activity history' : 'Recent system activity'}
            </CardDescription>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={fetchActivity}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        {error ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>Failed to load activity</p>
            <Button variant="outline" size="sm" onClick={fetchActivity} className="mt-2">
              Try Again
            </Button>
          </div>
        ) : activities.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No activity found</p>
          </div>
        ) : (
          <ScrollArea className="h-[400px]">
            <div className="space-y-4">
              {activities.map((activity, index) => (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className="relative">
                    <div className={`flex items-center justify-center h-8 w-8 rounded-full border-2 border-background ${actionColors[activity.action]}`}>
                      {actionIcons[activity.action]}
                    </div>
                    {index < activities.length - 1 && (
                      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-0.5 h-6 bg-border" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <Badge 
                        variant="secondary" 
                        className={`${actionColors[activity.action]} text-xs`}
                      >
                        {activity.action.replace('_', ' ')}
                      </Badge>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {formatDate(activity.created_at || new Date())}
                      </div>
                    </div>
                    
                    <p className="text-sm text-foreground mb-1">
                      {getActivityDescription(activity)}
                    </p>
                    
                    {activity.target_table_name && (
                      <p className="text-xs text-muted-foreground">
                        Table: {activity.target_table_name}
                        {activity.target_record_id && (
                          <span className="ml-2">ID: {activity.target_record_id.slice(0, 8)}...</span>
                        )}
                      </p>
                    )}
                    
                    {activity.ip_address && (
                      <p className="text-xs text-muted-foreground mt-1">
                        IP: {activity.ip_address}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
