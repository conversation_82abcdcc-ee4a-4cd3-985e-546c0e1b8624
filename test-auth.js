// Quick test script to verify authentication and database access
// Run this with: node test-auth.js

const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? '✅ Set' : '❌ Missing');
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', supabaseKey ? '✅ Set' : '❌ Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testDatabaseAccess() {
  console.log('🔍 Testing database access...\n');

  try {
    // Test 1: Check if we can connect to the database
    console.log('1. Testing database connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);

    if (connectionError) {
      console.error('❌ Database connection failed:', connectionError.message);
      return false;
    }
    console.log('✅ Database connection successful');

    // Test 2: Check if profiles table exists and has data
    console.log('\n2. Testing profiles table access...');
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select('id, email, full_name, role, created_at')
      .is('deleted_at', null)
      .limit(5);

    if (profilesError) {
      console.error('❌ Profiles table access failed:', profilesError.message);
      return false;
    }

    console.log('✅ Profiles table accessible');
    console.log(`📊 Found ${profiles.length} active profiles:`);
    profiles.forEach(profile => {
      console.log(`   - ${profile.email} (${profile.role}) - ${profile.full_name || 'No name'}`);
    });

    // Test 3: Check admin user specifically
    console.log('\n3. Testing admin user access...');
    const { data: adminUser, error: adminError } = await supabase
      .from('profiles')
      .select('*')
      .eq('email', '<EMAIL>')
      .is('deleted_at', null)
      .single();

    if (adminError) {
      console.error('❌ Admin user not found:', adminError.message);
      return false;
    }

    console.log('✅ Admin user found:');
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Name: ${adminUser.full_name}`);
    console.log(`   Role: ${adminUser.role}`);
    console.log(`   Created: ${adminUser.created_at}`);

    // Test 4: Test user_has_role function
    console.log('\n4. Testing user_has_role function...');
    const { data: roleTest, error: roleError } = await supabase
      .rpc('user_has_role', {
        user_id: adminUser.id,
        required_role: 'admin'
      });

    if (roleError) {
      console.error('❌ user_has_role function failed:', roleError.message);
      return false;
    }

    console.log(`✅ user_has_role function works: ${roleTest}`);

    // Test 5: Test RLS policies
    console.log('\n5. Testing RLS policies (this might fail without auth)...');
    const { data: rlsTest, error: rlsError } = await supabase
      .from('profiles')
      .select('id, email, role')
      .eq('id', adminUser.id);

    if (rlsError) {
      console.log('⚠️  RLS policy test failed (expected without auth):', rlsError.message);
    } else {
      console.log('✅ RLS policy allows access:', rlsTest);
    }

    return true;

  } catch (error) {
    console.error('❌ Unexpected error:', error);
    return false;
  }
}

async function testAuthFlow() {
  console.log('\n🔐 Testing authentication flow...\n');

  try {
    // Test login with admin credentials
    console.log('1. Testing login...');
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'your-password-here' // You'll need to replace this
    });

    if (authError) {
      console.log('⚠️  Login test skipped (no password provided)');
      console.log('   To test login, update the password in this script');
      return;
    }

    console.log('✅ Login successful');
    console.log(`   User ID: ${authData.user.id}`);
    console.log(`   Email: ${authData.user.email}`);

    // Test profile access with auth
    console.log('\n2. Testing profile access with authentication...');
    const { data: profileWithAuth, error: profileAuthError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .single();

    if (profileAuthError) {
      console.error('❌ Profile access with auth failed:', profileAuthError.message);
    } else {
      console.log('✅ Profile access with auth successful');
      console.log(`   Role: ${profileWithAuth.role}`);
    }

    // Sign out
    await supabase.auth.signOut();
    console.log('✅ Signed out successfully');

  } catch (error) {
    console.error('❌ Auth flow error:', error);
  }
}

async function main() {
  console.log('🚀 FSNC Dashboard Authentication Test\n');
  console.log('Environment:');
  console.log(`   Supabase URL: ${supabaseUrl}`);
  console.log(`   Anon Key: ${supabaseKey.substring(0, 20)}...`);
  console.log('');

  const dbSuccess = await testDatabaseAccess();
  
  if (dbSuccess) {
    await testAuthFlow();
  }

  console.log('\n📋 Summary:');
  console.log('   - If database tests pass but you still get permission errors in the app,');
  console.log('     the issue is likely in the frontend authentication flow.');
  console.log('   - Check the browser console for detailed error messages.');
  console.log('   - Use the AuthDebug component added to the dashboard to see auth state.');
}

main().catch(console.error);
