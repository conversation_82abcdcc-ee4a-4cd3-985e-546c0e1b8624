'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  DashboardStatsComponent,
  ActivityTimeline,
  ContentStatusOverview,
  QuickActionsPanel,
  CustomizableLayout,
  WidgetSkeleton,
} from '@/components/dashboard';
import { useDashboardData } from '@/hooks/use-dashboard-data';
import {
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { AuthDebug } from '@/components/debug/auth-debug';

export default function DashboardPage() {
  const {
    data,
    loading,
    isLoading,
    hasErrors,
    errorMessages,
    lastRefresh,
    refresh,
  } = useDashboardData();

  const [widgetVisibility, setWidgetVisibility] = useState({
    stats: true,
    activity: true,
    contentStatus: true,
    quickActions: true,
  });

  const handleWidgetToggle = (widgetId: string, visible: boolean) => {
    setWidgetVisibility((prev) => ({
      ...prev,
      [widgetId]: visible,
    }));
  };

  const widgets = [
    {
      id: 'stats',
      title: 'Dashboard Statistics',
      component: data.stats ? (
        <DashboardStatsComponent stats={data.stats} isLoading={loading.stats} />
      ) : (
        <WidgetSkeleton />
      ),
      visible: widgetVisibility.stats,
      defaultSize: 60,
      minSize: 40,
      collapsible: true,
    },
    {
      id: 'activity',
      title: 'Recent Activity',
      component: (
        <ActivityTimeline
          activities={data.recentActivity}
          isLoading={loading.recentActivity}
          onRefresh={refresh.recentActivity}
        />
      ),
      visible: widgetVisibility.activity,
      defaultSize: 40,
      minSize: 30,
      collapsible: true,
    },
    {
      id: 'contentStatus',
      title: 'Content Status Overview',
      component: data.statusBreakdown ? (
        <ContentStatusOverview
          statusBreakdown={data.statusBreakdown}
          systemHealth={data.systemHealth}
          isLoading={loading.statusBreakdown || loading.systemHealth}
        />
      ) : (
        <WidgetSkeleton />
      ),
      visible: widgetVisibility.contentStatus,
      defaultSize: 50,
      minSize: 40,
      collapsible: true,
    },
    {
      id: 'quickActions',
      title: 'Quick Actions',
      component: (
        <QuickActionsPanel
          onActionClick={(actionId) => console.log('Action clicked:', actionId)}
        />
      ),
      visible: widgetVisibility.quickActions,
      defaultSize: 50,
      minSize: 30,
      collapsible: true,
    },
  ];

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      {/* Authentication Debug Panel - Remove this in production */}
      <AuthDebug />

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome to your FSNC content management dashboard
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div className="text-right">
            <p className="text-sm text-muted-foreground">
              Last updated:{' '}
              {formatDistanceToNow(lastRefresh, { addSuffix: true })}
            </p>
            {isLoading && (
              <Badge variant="secondary" className="flex items-center gap-1">
                <RefreshCw className="h-3 w-3 animate-spin" />
                Updating...
              </Badge>
            )}
          </div>

          <Button
            variant="outline"
            onClick={refresh.all}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw
              className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`}
            />
            Refresh All
          </Button>
        </div>
      </div>

      {/* Error Messages */}
      {hasErrors && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p className="font-medium">Some data could not be loaded:</p>
              <ul className="list-disc list-inside text-sm">
                {errorMessages.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Quick Stats Overview */}
      {data.stats && (
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Content
              </CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {(
                  data.stats.totalPosts + data.stats.totalPages
                ).toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {data.stats.totalPosts} posts, {data.stats.totalPages} pages
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Published</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                {data.stats.publishedPosts.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {data.stats.totalPosts > 0
                  ? Math.round(
                      (data.stats.publishedPosts / data.stats.totalPosts) * 100
                    )
                  : 0}
                % of total posts
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Pending Review
              </CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {data.stats.pendingReviewPosts.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">Awaiting approval</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Active Users
              </CardTitle>
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {data.stats.totalUsers.toLocaleString()}
              </div>
              <p className="text-xs text-muted-foreground">
                {data.stats.pendingInvitations} pending invitations
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Customizable Widget Layout */}
      <CustomizableLayout
        widgets={widgets}
        onWidgetToggle={handleWidgetToggle}
        onLayoutChange={(layout) => {
          console.log('Layout changed:', layout);
          // Here you could save the layout to user preferences
        }}
      />
    </div>
  );
}
