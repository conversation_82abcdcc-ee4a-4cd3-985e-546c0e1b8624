'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  X,
  Calendar as CalendarIcon,
  Users,
  Shield,
  Edit,
  User,
} from 'lucide-react';
import { format } from 'date-fns';
import type { QueryParams, UserRole } from '@/types';

interface UsersFiltersProps {
  filters: QueryParams;
  onFiltersChange: (filters: Partial<QueryParams>) => void;
  onReset: () => void;
  loading?: boolean;
}

const roleOptions: { value: UserRole; label: string; icon: React.ReactNode }[] =
  [
    { value: 'admin', label: 'Admin', icon: <Shield className="h-3 w-3" /> },
    {
      value: 'publisher',
      label: 'Publisher',
      icon: <Edit className="h-3 w-3" />,
    },
    { value: 'editor', label: 'Editor', icon: <Edit className="h-3 w-3" /> },
    { value: 'member', label: 'Member', icon: <User className="h-3 w-3" /> },
  ];

const sortOptions = [
  { value: 'created_at', label: 'Date Joined' },
  { value: 'updated_at', label: 'Last Active' },
  { value: 'full_name', label: 'Name' },
  { value: 'email', label: 'Email' },
  { value: 'role', label: 'Role' },
];

export function UsersFilters({
  filters,
  onFiltersChange,
  onReset,
  loading = false,
}: UsersFiltersProps) {
  const [dateFrom, setDateFrom] = useState<Date | undefined>(
    filters.dateFrom ? new Date(filters.dateFrom) : undefined
  );
  const [dateTo, setDateTo] = useState<Date | undefined>(
    filters.dateTo ? new Date(filters.dateTo) : undefined
  );

  const handleSearchChange = (value: string) => {
    onFiltersChange({ search: value || undefined });
  };

  const handleRoleChange = (value: string) => {
    onFiltersChange({ status: value === 'all' ? undefined : value });
  };

  const handleSortChange = (value: string) => {
    const [sortBy, sortOrder] = value.split('-');
    onFiltersChange({ sortBy, sortOrder: sortOrder as 'asc' | 'desc' });
  };

  const handleDateFromChange = (date: Date | undefined) => {
    setDateFrom(date);
    onFiltersChange({
      dateFrom: date ? date.toISOString() : undefined,
    });
  };

  const handleDateToChange = (date: Date | undefined) => {
    setDateTo(date);
    onFiltersChange({
      dateTo: date ? date.toISOString() : undefined,
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.status) count++;
    if (filters.dateFrom) count++;
    if (filters.dateTo) count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  const handleReset = () => {
    setDateFrom(undefined);
    setDateTo(undefined);
    onReset();
  };

  return (
    <div className="space-y-4">
      {/* Search and Quick Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search users by name or email..."
            value={filters.search || ''}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
            disabled={loading}
          />
        </div>

        <div className="flex gap-2">
          <Select
            value={filters.status || 'all'}
            onValueChange={handleRoleChange}
            disabled={loading}
          >
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="All Roles" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">
                <div className="flex items-center gap-2">
                  <Users className="h-3 w-3" />
                  All Roles
                </div>
              </SelectItem>
              {roleOptions.map((role) => (
                <SelectItem key={role.value} value={role.value}>
                  <div className="flex items-center gap-2">
                    {role.icon}
                    {role.label}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={`${filters.sortBy || 'created_at'}-${
              filters.sortOrder || 'desc'
            }`}
            onValueChange={handleSortChange}
            disabled={loading}
          >
            <SelectTrigger className="w-[160px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <React.Fragment key={option.value}>
                  <SelectItem value={`${option.value}-desc`}>
                    {option.label} (Newest)
                  </SelectItem>
                  <SelectItem value={`${option.value}-asc`}>
                    {option.label} (Oldest)
                  </SelectItem>
                </React.Fragment>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Advanced Filters */}
      <div className="flex flex-wrap items-center gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" disabled={loading}>
              <CalendarIcon className="mr-2 h-4 w-4" />
              From Date
              {dateFrom && (
                <Badge variant="secondary" className="ml-2">
                  {format(dateFrom, 'MMM dd')}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={dateFrom}
              onSelect={handleDateFromChange}
              initialFocus
            />
          </PopoverContent>
        </Popover>

        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" disabled={loading}>
              <CalendarIcon className="mr-2 h-4 w-4" />
              To Date
              {dateTo && (
                <Badge variant="secondary" className="ml-2">
                  {format(dateTo, 'MMM dd')}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={dateTo}
              onSelect={handleDateToChange}
              initialFocus
            />
          </PopoverContent>
        </Popover>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            disabled={loading}
          >
            <X className="mr-2 h-4 w-4" />
            Clear Filters
            <Badge variant="secondary" className="ml-2">
              {activeFiltersCount}
            </Badge>
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filters.search && (
            <Badge variant="outline" className="gap-1">
              Search: {filters.search}
              <button
                onClick={() => handleSearchChange('')}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}

          {filters.status && (
            <Badge variant="outline" className="gap-1">
              Role: {filters.status}
              <button
                onClick={() => handleRoleChange('all')}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}

          {filters.dateFrom && (
            <Badge variant="outline" className="gap-1">
              From: {format(new Date(filters.dateFrom), 'MMM dd, yyyy')}
              <button
                onClick={() => handleDateFromChange(undefined)}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}

          {filters.dateTo && (
            <Badge variant="outline" className="gap-1">
              To: {format(new Date(filters.dateTo), 'MMM dd, yyyy')}
              <button
                onClick={() => handleDateToChange(undefined)}
                className="ml-1 hover:bg-muted rounded-full p-0.5"
              >
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}
