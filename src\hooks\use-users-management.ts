'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  getUsers, 
  getInvitations,
  updateProfile,
  deleteUser,
  bulkUpdateUserRoles,
  bulkDeleteUsers,
  createInvitation,
  resendInvitation,
  cancelInvitation,
  bulkCreateInvitations,
  getUserActivity
} from '@/lib/database-helpers';
import type { 
  Profile, 
  Invitation,
  InvitationWithRelations,
  AuditLog,
  QueryParams, 
  UserRole,
  PaginatedResponse 
} from '@/types';

interface UsersManagementState {
  users: Profile[];
  invitations: InvitationWithRelations[];
  userActivity: AuditLog[];
  pagination: {
    users?: PaginatedResponse<Profile>['pagination'];
    invitations?: PaginatedResponse<InvitationWithRelations>['pagination'];
    activity?: PaginatedResponse<AuditLog>['pagination'];
  };
  loading: {
    users: boolean;
    invitations: boolean;
    activity: boolean;
    actions: boolean;
  };
  errors: {
    users?: string;
    invitations?: string;
    activity?: string;
    actions?: string;
  };
}

export function useUsersManagement() {
  const [state, setState] = useState<UsersManagementState>({
    users: [],
    invitations: [],
    userActivity: [],
    pagination: {},
    loading: {
      users: true,
      invitations: true,
      activity: true,
      actions: false,
    },
    errors: {},
  });

  const [filters, setFilters] = useState<QueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  const [invitationFilters, setInvitationFilters] = useState<QueryParams>({
    page: 1,
    limit: 10,
    sortBy: 'created_at',
    sortOrder: 'desc',
  });

  // Fetch users with current filters
  const fetchUsers = useCallback(async () => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, users: true },
      errors: { ...prev.errors, users: undefined }
    }));

    try {
      const response = await getUsers(filters);
      
      if (response.error) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, users: false },
          errors: { ...prev.errors, users: response.error }
        }));
        return;
      }

      setState(prev => ({
        ...prev,
        users: response.data || [],
        pagination: { ...prev.pagination, users: response.pagination },
        loading: { ...prev.loading, users: false },
        errors: { ...prev.errors, users: undefined }
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, users: false },
        errors: { 
          ...prev.errors, 
          users: error instanceof Error ? error.message : 'Failed to fetch users'
        }
      }));
    }
  }, [filters]);

  // Fetch invitations with current filters
  const fetchInvitations = useCallback(async () => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, invitations: true },
      errors: { ...prev.errors, invitations: undefined }
    }));

    try {
      const response = await getInvitations(invitationFilters);
      
      if (response.error) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, invitations: false },
          errors: { ...prev.errors, invitations: response.error }
        }));
        return;
      }

      setState(prev => ({
        ...prev,
        invitations: response.data || [],
        pagination: { ...prev.pagination, invitations: response.pagination },
        loading: { ...prev.loading, invitations: false },
        errors: { ...prev.errors, invitations: undefined }
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, invitations: false },
        errors: { 
          ...prev.errors, 
          invitations: error instanceof Error ? error.message : 'Failed to fetch invitations'
        }
      }));
    }
  }, [invitationFilters]);

  // Fetch user activity
  const fetchUserActivity = useCallback(async (userId?: string) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, activity: true },
      errors: { ...prev.errors, activity: undefined }
    }));

    try {
      const response = await getUserActivity(userId, {
        page: 1,
        limit: 20,
        sortBy: 'created_at',
        sortOrder: 'desc'
      });
      
      if (response.error) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, activity: false },
          errors: { ...prev.errors, activity: response.error }
        }));
        return;
      }

      setState(prev => ({
        ...prev,
        userActivity: response.data || [],
        pagination: { ...prev.pagination, activity: response.pagination },
        loading: { ...prev.loading, activity: false },
        errors: { ...prev.errors, activity: undefined }
      }));
    } catch (error) {
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, activity: false },
        errors: { 
          ...prev.errors, 
          activity: error instanceof Error ? error.message : 'Failed to fetch activity'
        }
      }));
    }
  }, []);

  // Update user filters
  const updateFilters = useCallback((newFilters: Partial<QueryParams>) => {
    setFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  }, []);

  // Update invitation filters
  const updateInvitationFilters = useCallback((newFilters: Partial<QueryParams>) => {
    setInvitationFilters(prev => ({ ...prev, ...newFilters, page: 1 }));
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters({
      page: 1,
      limit: 10,
      sortBy: 'created_at',
      sortOrder: 'desc',
    });
  }, []);

  // User actions
  const handleUpdateUser = useCallback(async (
    id: string, 
    updates: Partial<Pick<Profile, 'full_name' | 'avatar_url' | 'role'>>
  ) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await updateProfile(id, updates);
      
      if (result.error) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error }
        }));
        return { success: false, error: result.error };
      }

      // Update the user in the local state
      setState(prev => ({
        ...prev,
        users: prev.users.map(user => 
          user.id === id ? { ...user, ...updates } : user
        ),
        loading: { ...prev.loading, actions: false }
      }));

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  }, []);

  const handleDeleteUser = useCallback(async (id: string) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await deleteUser(id);
      
      if (result.error) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error }
        }));
        return { success: false, error: result.error };
      }

      // Remove the user from local state
      setState(prev => ({
        ...prev,
        users: prev.users.filter(user => user.id !== id),
        loading: { ...prev.loading, actions: false }
      }));

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete user';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  }, []);

  // Bulk operations
  const handleBulkRoleChange = useCallback(async (userIds: string[], role: UserRole) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await bulkUpdateUserRoles(userIds, role);
      
      if (result.error) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error }
        }));
        return { success: false, error: result.error };
      }

      // Update users in local state
      setState(prev => ({
        ...prev,
        users: prev.users.map(user => 
          userIds.includes(user.id) ? { ...user, role } : user
        ),
        loading: { ...prev.loading, actions: false }
      }));

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update user roles';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  }, []);

  const handleBulkDelete = useCallback(async (userIds: string[]) => {
    setState(prev => ({ 
      ...prev, 
      loading: { ...prev.loading, actions: true },
      errors: { ...prev.errors, actions: undefined }
    }));

    try {
      const result = await bulkDeleteUsers(userIds);
      
      if (result.error) {
        setState(prev => ({
          ...prev,
          loading: { ...prev.loading, actions: false },
          errors: { ...prev.errors, actions: result.error }
        }));
        return { success: false, error: result.error };
      }

      // Remove users from local state
      setState(prev => ({
        ...prev,
        users: prev.users.filter(user => !userIds.includes(user.id)),
        loading: { ...prev.loading, actions: false }
      }));

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete users';
      setState(prev => ({
        ...prev,
        loading: { ...prev.loading, actions: false },
        errors: { ...prev.errors, actions: errorMessage }
      }));
      return { success: false, error: errorMessage };
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  useEffect(() => {
    fetchInvitations();
  }, [fetchInvitations]);

  useEffect(() => {
    fetchUserActivity();
  }, [fetchUserActivity]);

  return {
    // State
    users: state.users,
    invitations: state.invitations,
    userActivity: state.userActivity,
    pagination: state.pagination,
    loading: state.loading,
    errors: state.errors,
    filters,
    invitationFilters,

    // Actions
    updateFilters,
    updateInvitationFilters,
    resetFilters,
    refresh: fetchUsers,
    refreshInvitations: fetchInvitations,
    refreshActivity: fetchUserActivity,
    
    // User actions
    updateUser: handleUpdateUser,
    deleteUser: handleDeleteUser,
    
    // Bulk actions
    bulkRoleChange: handleBulkRoleChange,
    bulkDelete: handleBulkDelete,

    // Invitation actions (to be implemented in components)
    createInvitation,
    resendInvitation,
    cancelInvitation,
    bulkCreateInvitations,
  };
}
