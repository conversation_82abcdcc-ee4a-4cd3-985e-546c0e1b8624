'use client';

import { useState, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Hash, Search, Filter, Zap } from 'lucide-react';
import type { Tag } from '@/types';

interface TagWithStats extends Tag {
  post_count?: number;
  usage_trend?: 'up' | 'down' | 'stable';
}

interface TagCloudProps {
  tags: TagWithStats[];
  onTagClick?: (tag: TagWithStats) => void;
  onTagEdit?: (tag: TagWithStats) => void;
  maxTags?: number;
}

export function TagCloud({ 
  tags, 
  onTagClick, 
  onTagEdit,
  maxTags = 50 
}: TagCloudProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'usage' | 'alphabetical' | 'recent'>('usage');
  const [showAll, setShowAll] = useState(false);

  // Calculate tag sizes based on usage
  const processedTags = useMemo(() => {
    let filtered = tags.filter(tag =>
      tag.name.toLowerCase().includes(searchQuery.toLowerCase())
    );

    // Sort tags
    switch (sortBy) {
      case 'usage':
        filtered.sort((a, b) => (b.post_count || 0) - (a.post_count || 0));
        break;
      case 'alphabetical':
        filtered.sort((a, b) => a.name.localeCompare(b.name));
        break;
      case 'recent':
        filtered.sort((a, b) => 
          new Date(b.created_at || '').getTime() - new Date(a.created_at || '').getTime()
        );
        break;
    }

    // Limit tags if not showing all
    if (!showAll && filtered.length > maxTags) {
      filtered = filtered.slice(0, maxTags);
    }

    // Calculate sizes
    const maxUsage = Math.max(...filtered.map(tag => tag.post_count || 0), 1);
    const minUsage = Math.min(...filtered.map(tag => tag.post_count || 0));
    const usageRange = maxUsage - minUsage || 1;

    return filtered.map(tag => {
      const usage = tag.post_count || 0;
      const normalizedUsage = (usage - minUsage) / usageRange;
      
      // Size classes from smallest to largest
      const sizeClasses = [
        'text-xs',
        'text-sm', 
        'text-base',
        'text-lg',
        'text-xl',
        'text-2xl'
      ];
      
      const sizeIndex = Math.floor(normalizedUsage * (sizeClasses.length - 1));
      const sizeClass = sizeClasses[sizeIndex];

      // Color intensity based on usage
      const opacity = Math.max(0.4, normalizedUsage);

      return {
        ...tag,
        sizeClass,
        opacity,
        usage
      };
    });
  }, [tags, searchQuery, sortBy, showAll, maxTags]);

  const getTrendColor = (trend?: string) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      default:
        return 'text-muted-foreground';
    }
  };

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return '↗️';
      case 'down':
        return '↘️';
      default:
        return '→';
    }
  };

  if (tags.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Hash className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tags found</h3>
          <p className="text-muted-foreground text-center mb-4">
            Create your first tag to start organizing your content.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Hash className="h-5 w-5" />
            Tag Cloud
          </CardTitle>
          <Badge variant="secondary">
            {processedTags.length} of {tags.length} tags
          </Badge>
        </div>
        
        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mt-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search tags..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9"
            />
          </div>
          
          <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="usage">By Usage</SelectItem>
              <SelectItem value="alphabetical">Alphabetical</SelectItem>
              <SelectItem value="recent">Most Recent</SelectItem>
            </SelectContent>
          </Select>

          {tags.length > maxTags && (
            <Button
              variant="outline"
              onClick={() => setShowAll(!showAll)}
              className="whitespace-nowrap"
            >
              {showAll ? 'Show Less' : `Show All (${tags.length})`}
            </Button>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        {processedTags.length === 0 ? (
          <div className="text-center py-8">
            <Filter className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground">
              No tags match your search criteria.
            </p>
          </div>
        ) : (
          <div className="flex flex-wrap gap-2 leading-relaxed">
            {processedTags.map((tag) => (
              <Button
                key={tag.id}
                variant="ghost"
                className={`
                  ${tag.sizeClass} 
                  h-auto p-2 
                  hover:bg-primary/10 
                  transition-all duration-200
                  ${getTrendColor(tag.usage_trend)}
                `}
                style={{ opacity: tag.opacity }}
                onClick={() => onTagClick?.(tag)}
                onDoubleClick={() => onTagEdit?.(tag)}
                title={`${tag.name} - ${tag.usage} posts ${getTrendIcon(tag.usage_trend)}`}
              >
                <span className="flex items-center gap-1">
                  #{tag.name}
                  {tag.usage_trend && tag.usage_trend !== 'stable' && (
                    <span className="text-xs">
                      {getTrendIcon(tag.usage_trend)}
                    </span>
                  )}
                </span>
              </Button>
            ))}
          </div>
        )}

        {/* Legend */}
        <div className="mt-6 pt-4 border-t">
          <div className="flex flex-wrap items-center gap-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-2">
              <Zap className="h-3 w-3" />
              <span>Size = Usage frequency</span>
            </div>
            <div className="flex items-center gap-2">
              <span>↗️ Trending up</span>
              <span>↘️ Trending down</span>
              <span>→ Stable</span>
            </div>
            <div>
              <span>Double-click to edit</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
