'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  ChevronRight,
  ChevronDown,
  MoreHorizontal,
  Edit,
  Plus,
  Trash2,
  FileText,
  Folder,
  FolderOpen,
} from 'lucide-react';
import type { Category } from '@/types';

interface CategoryHierarchyProps {
  categories: Category[];
  onEdit?: (category: Category) => void;
  onDelete?: (category: Category) => void;
  onAddChild?: (parentCategory: Category) => void;
  onViewPosts?: (category: Category) => void;
}

interface CategoryNodeProps {
  category: Category;
  children: Category[];
  allCategories: Category[];
  level: number;
  onEdit?: (category: Category) => void;
  onDelete?: (category: Category) => void;
  onAddChild?: (parentCategory: Category) => void;
  onViewPosts?: (category: Category) => void;
}

function CategoryNode({
  category,
  children,
  allCategories,
  level,
  onEdit,
  onDelete,
  onAddChild,
  onViewPosts,
}: CategoryNodeProps) {
  const [isExpanded, setIsExpanded] = useState(level < 2); // Auto-expand first 2 levels
  const hasChildren = children.length > 0;
  const postCount = category.post_count || 0;

  const indentClass = level > 0 ? `ml-${Math.min(level * 4, 16)}` : '';

  return (
    <div className="space-y-1">
      <div
        className={`flex items-center gap-2 p-2 rounded-lg hover:bg-muted/50 ${indentClass}`}
      >
        {/* Expand/Collapse Button */}
        {hasChildren ? (
          <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
            <CollapsibleTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                {isExpanded ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronRight className="h-3 w-3" />
                )}
              </Button>
            </CollapsibleTrigger>
          </Collapsible>
        ) : (
          <div className="w-6" />
        )}

        {/* Category Icon */}
        <div className="flex-shrink-0">
          {hasChildren ? (
            isExpanded ? (
              <FolderOpen className="h-4 w-4 text-blue-500" />
            ) : (
              <Folder className="h-4 w-4 text-blue-500" />
            )
          ) : (
            <FileText className="h-4 w-4 text-muted-foreground" />
          )}
        </div>

        {/* Category Info */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="font-medium truncate">{category.name}</span>
            {postCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {postCount}
              </Badge>
            )}
          </div>
          {category.description && (
            <p className="text-xs text-muted-foreground truncate mt-1">
              {category.description}
            </p>
          )}
        </div>

        {/* Actions */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
              <MoreHorizontal className="h-3 w-3" />
              <span className="sr-only">Category actions</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onViewPosts && (
              <DropdownMenuItem onClick={() => onViewPosts(category)}>
                <FileText className="mr-2 h-4 w-4" />
                View Posts ({postCount})
              </DropdownMenuItem>
            )}
            {onEdit && (
              <DropdownMenuItem onClick={() => onEdit(category)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
            )}
            {onAddChild && (
              <DropdownMenuItem onClick={() => onAddChild(category)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Subcategory
              </DropdownMenuItem>
            )}
            {onDelete && (
              <DropdownMenuItem
                onClick={() => onDelete(category)}
                className="text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Children */}
      {hasChildren && (
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-1">
            {children.map((child) => {
              const grandChildren = allCategories.filter(
                (c) => c.parent_id === child.id
              );
              return (
                <CategoryNode
                  key={child.id}
                  category={child}
                  children={grandChildren}
                  allCategories={allCategories}
                  level={level + 1}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onAddChild={onAddChild}
                  onViewPosts={onViewPosts}
                />
              );
            })}
          </CollapsibleContent>
        </Collapsible>
      )}
    </div>
  );
}

export function CategoryHierarchy({
  categories,
  onEdit,
  onDelete,
  onAddChild,
  onViewPosts,
}: CategoryHierarchyProps) {
  // Build hierarchy
  const topLevelCategories = categories.filter((cat) => !cat.parent_id);
  const totalCategories = categories.length;
  const totalPosts = categories.reduce(
    (sum, cat) => sum + (cat.post_count || 0),
    0
  );

  if (categories.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <Folder className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-semibold mb-2">No categories found</h3>
          <p className="text-muted-foreground text-center mb-4">
            Create your first category to start organizing your content.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Folder className="h-5 w-5" />
            Category Hierarchy
          </CardTitle>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>{totalCategories} categories</span>
            <span>{totalPosts} posts</span>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-2">
        {topLevelCategories.map((category) => {
          const children = categories.filter(
            (c) => c.parent_id === category.id
          );
          return (
            <CategoryNode
              key={category.id}
              category={category}
              children={children}
              allCategories={categories}
              level={0}
              onEdit={onEdit}
              onDelete={onDelete}
              onAddChild={onAddChild}
              onViewPosts={onViewPosts}
            />
          );
        })}
      </CardContent>
    </Card>
  );
}
