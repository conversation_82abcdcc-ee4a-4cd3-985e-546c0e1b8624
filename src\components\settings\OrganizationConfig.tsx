'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Building2, 
  Globe, 
  Mail, 
  Upload, 
  ExternalLink,
  AlertTriangle,
  Twitter,
  Linkedin,
  Facebook,
  Instagram
} from 'lucide-react';

interface OrganizationConfigProps {
  settings: Record<string, any>;
  loading: { settings: boolean; saving: boolean };
  errors: Record<string, string | undefined>;
  onUpdateSetting: (key: string, value: any) => void;
}

export function OrganizationConfig({ 
  settings, 
  loading, 
  errors, 
  onUpdateSetting 
}: OrganizationConfigProps) {
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [faviconFile, setFaviconFile] = useState<File | null>(null);

  const handleInputChange = (key: string, value: string) => {
    onUpdateSetting(key, value);
  };

  const handleSocialMediaChange = (platform: string, value: string) => {
    const currentSocial = settings.social_media || {};
    onUpdateSetting('social_media', {
      ...currentSocial,
      [platform]: value,
    });
  };

  const handleFileUpload = async (file: File, type: 'logo' | 'favicon') => {
    // TODO: Implement file upload to Supabase Storage
    console.log(`Uploading ${type}:`, file.name);
    // For now, just store the file name
    if (type === 'logo') {
      setLogoFile(file);
      onUpdateSetting('logo_url', `/uploads/${file.name}`);
    } else {
      setFaviconFile(file);
      onUpdateSetting('favicon_url', `/uploads/${file.name}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Basic Organization Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Basic Information
          </CardTitle>
          <CardDescription>
            Configure your organization's basic details and public information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="org-name">Organization Name</Label>
              <Input
                id="org-name"
                value={settings.organization_name || ''}
                onChange={(e) => handleInputChange('organization_name', e.target.value)}
                placeholder="Enter organization name"
                disabled={loading.saving}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="contact-email">Contact Email</Label>
              <Input
                id="contact-email"
                type="email"
                value={settings.contact_email || ''}
                onChange={(e) => handleInputChange('contact_email', e.target.value)}
                placeholder="<EMAIL>"
                disabled={loading.saving}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="org-description">Description</Label>
            <Textarea
              id="org-description"
              value={settings.organization_description || ''}
              onChange={(e) => handleInputChange('organization_description', e.target.value)}
              placeholder="Brief description of your organization"
              rows={3}
              disabled={loading.saving}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="website-url">Website URL</Label>
            <div className="flex">
              <Input
                id="website-url"
                type="url"
                value={settings.website_url || ''}
                onChange={(e) => handleInputChange('website_url', e.target.value)}
                placeholder="https://your-website.com"
                disabled={loading.saving}
              />
              {settings.website_url && (
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-2"
                  onClick={() => window.open(settings.website_url, '_blank')}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Branding */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Branding & Assets
          </CardTitle>
          <CardDescription>
            Upload and manage your organization's visual identity
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Logo Upload */}
            <div className="space-y-2">
              <Label>Organization Logo</Label>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                {settings.logo_url ? (
                  <div className="space-y-2">
                    <img
                      src={settings.logo_url}
                      alt="Organization Logo"
                      className="mx-auto h-16 w-auto"
                    />
                    <p className="text-sm text-muted-foreground">Current logo</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="mx-auto h-8 w-8 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">No logo uploaded</p>
                  </div>
                )}
                <input
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(file, 'logo');
                  }}
                  className="mt-2"
                  disabled={loading.saving}
                />
              </div>
            </div>

            {/* Favicon Upload */}
            <div className="space-y-2">
              <Label>Favicon</Label>
              <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                {settings.favicon_url ? (
                  <div className="space-y-2">
                    <img
                      src={settings.favicon_url}
                      alt="Favicon"
                      className="mx-auto h-8 w-8"
                    />
                    <p className="text-sm text-muted-foreground">Current favicon</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <Upload className="mx-auto h-8 w-8 text-muted-foreground" />
                    <p className="text-sm text-muted-foreground">No favicon uploaded</p>
                  </div>
                )}
                <input
                  type="file"
                  accept="image/x-icon,image/png"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(file, 'favicon');
                  }}
                  className="mt-2"
                  disabled={loading.saving}
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Social Media Links */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Social Media Links
          </CardTitle>
          <CardDescription>
            Configure your organization's social media presence
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="twitter" className="flex items-center gap-2">
                <Twitter className="h-4 w-4" />
                Twitter/X
              </Label>
              <Input
                id="twitter"
                value={settings.social_media?.twitter || ''}
                onChange={(e) => handleSocialMediaChange('twitter', e.target.value)}
                placeholder="https://twitter.com/yourorg"
                disabled={loading.saving}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="linkedin" className="flex items-center gap-2">
                <Linkedin className="h-4 w-4" />
                LinkedIn
              </Label>
              <Input
                id="linkedin"
                value={settings.social_media?.linkedin || ''}
                onChange={(e) => handleSocialMediaChange('linkedin', e.target.value)}
                placeholder="https://linkedin.com/company/yourorg"
                disabled={loading.saving}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="facebook" className="flex items-center gap-2">
                <Facebook className="h-4 w-4" />
                Facebook
              </Label>
              <Input
                id="facebook"
                value={settings.social_media?.facebook || ''}
                onChange={(e) => handleSocialMediaChange('facebook', e.target.value)}
                placeholder="https://facebook.com/yourorg"
                disabled={loading.saving}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="instagram" className="flex items-center gap-2">
                <Instagram className="h-4 w-4" />
                Instagram
              </Label>
              <Input
                id="instagram"
                value={settings.social_media?.instagram || ''}
                onChange={(e) => handleSocialMediaChange('instagram', e.target.value)}
                placeholder="https://instagram.com/yourorg"
                disabled={loading.saving}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Newsletter Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Newsletter Configuration
          </CardTitle>
          <CardDescription>
            Configure newsletter functionality and settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Newsletter</Label>
              <p className="text-sm text-muted-foreground">
                Allow users to subscribe to your newsletter
              </p>
            </div>
            <Switch
              checked={settings.newsletter_enabled || false}
              onCheckedChange={(checked) => onUpdateSetting('newsletter_enabled', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
