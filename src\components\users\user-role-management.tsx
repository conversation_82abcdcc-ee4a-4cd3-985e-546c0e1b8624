'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Shield, 
  Edit, 
  User, 
  Save, 
  X,
  CheckCircle,
  AlertCircle,
  Users,
  Settings
} from 'lucide-react';
import type { Profile, UserRole } from '@/types';

interface UserRoleManagementProps {
  user: Profile;
  onRoleChange: (userId: string, newRole: UserRole) => Promise<{ success: boolean; error?: string }>;
  onClose?: () => void;
  className?: string;
}

const roleOptions: { 
  value: UserRole; 
  label: string; 
  icon: React.ReactNode; 
  description: string;
  permissions: string[];
}[] = [
  { 
    value: 'admin', 
    label: 'Administrator', 
    icon: <Shield className="h-4 w-4" />,
    description: 'Full system access with all administrative privileges',
    permissions: [
      'Manage all users and roles',
      'Access system settings',
      'Create, edit, and delete all content',
      'Manage invitations and access control',
      'View audit logs and analytics',
      'Configure organization settings'
    ]
  },
  { 
    value: 'publisher', 
    label: 'Publisher', 
    icon: <Edit className="h-4 w-4" />,
    description: 'Can create, edit, and publish content',
    permissions: [
      'Create and edit posts and pages',
      'Publish and unpublish content',
      'Manage categories and tags',
      'Upload and manage media',
      'View content analytics',
      'Manage own profile'
    ]
  },
  { 
    value: 'editor', 
    label: 'Editor', 
    icon: <Edit className="h-4 w-4" />,
    description: 'Can create and edit content but cannot publish',
    permissions: [
      'Create and edit posts and pages',
      'Save drafts and submit for review',
      'Manage categories and tags',
      'Upload and manage media',
      'View content in draft state',
      'Manage own profile'
    ]
  },
  { 
    value: 'member', 
    label: 'Member', 
    icon: <User className="h-4 w-4" />,
    description: 'Read-only access to published content',
    permissions: [
      'View published content',
      'Access public pages',
      'View own profile',
      'Basic dashboard access'
    ]
  },
];

const roleColors: Record<UserRole, string> = {
  admin: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  publisher: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300',
  editor: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  member: 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300',
};

export function UserRoleManagement({
  user,
  onRoleChange,
  onClose,
  className,
}: UserRoleManagementProps) {
  const [selectedRole, setSelectedRole] = useState<UserRole>(user.role);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  const currentRoleOption = roleOptions.find(option => option.value === user.role);
  const selectedRoleOption = roleOptions.find(option => option.value === selectedRole);
  const hasChanges = selectedRole !== user.role;

  const handleSave = async () => {
    if (!hasChanges) return;

    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await onRoleChange(user.id, selectedRole);
      
      if (result.success) {
        setSuccess(`Role updated to ${selectedRole} successfully`);
        setTimeout(() => {
          setSuccess(null);
          onClose?.();
        }, 2000);
      } else {
        setError(result.error || 'Failed to update role');
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update role');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setSelectedRole(user.role);
    setError(null);
    setSuccess(null);
    onClose?.();
  };

  const getInitials = (name?: string) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(n => n[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Settings className="h-5 w-5" />
          Role Management
        </CardTitle>
        <CardDescription>
          Manage user permissions and access levels
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* User Info */}
        <div className="flex items-center space-x-4 p-4 border rounded-lg bg-muted/50">
          <Avatar className="h-12 w-12">
            <AvatarImage src={user.avatar_url || undefined} />
            <AvatarFallback>
              {getInitials(user.full_name)}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h3 className="font-semibold">{user.full_name || 'Unnamed User'}</h3>
            <p className="text-sm text-muted-foreground">{user.email}</p>
            <div className="flex items-center gap-2 mt-1">
              <span className="text-xs text-muted-foreground">Current Role:</span>
              <Badge 
                variant="secondary" 
                className={`${roleColors[user.role]} flex items-center gap-1`}
              >
                {currentRoleOption?.icon}
                {currentRoleOption?.label}
              </Badge>
            </div>
          </div>
        </div>

        {/* Role Selection */}
        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select New Role</label>
            <Select value={selectedRole} onValueChange={(value: UserRole) => setSelectedRole(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {roleOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      {option.icon}
                      <div>
                        <div className="font-medium">{option.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {option.description}
                        </div>
                      </div>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Role Details */}
          {selectedRoleOption && (
            <div className="p-4 border rounded-lg bg-muted/30">
              <div className="flex items-center gap-2 mb-3">
                <Badge 
                  variant="secondary" 
                  className={`${roleColors[selectedRole]} flex items-center gap-1`}
                >
                  {selectedRoleOption.icon}
                  {selectedRoleOption.label}
                </Badge>
                {hasChanges && (
                  <Badge variant="outline" className="text-xs">
                    New Role
                  </Badge>
                )}
              </div>
              
              <p className="text-sm text-muted-foreground mb-3">
                {selectedRoleOption.description}
              </p>
              
              <div className="space-y-2">
                <h4 className="text-sm font-medium flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  Permissions
                </h4>
                <ul className="space-y-1">
                  {selectedRoleOption.permissions.map((permission, index) => (
                    <li key={index} className="text-xs text-muted-foreground flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      {permission}
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Alerts */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex items-center gap-2 pt-4 border-t">
          <Button
            onClick={handleSave}
            disabled={!hasChanges || loading}
            className="flex-1"
          >
            <Save className="mr-2 h-4 w-4" />
            {loading ? 'Updating...' : 'Update Role'}
          </Button>
          
          {onClose && (
            <Button
              variant="outline"
              onClick={handleCancel}
              disabled={loading}
            >
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
          )}
        </div>

        {/* Warning for Role Changes */}
        {hasChanges && (
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Changing this user's role will immediately update their permissions and access levels.
              {selectedRole === 'admin' && ' Admin users have full system access.'}
              {user.role === 'admin' && selectedRole !== 'admin' && ' Removing admin privileges will restrict their access.'}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
