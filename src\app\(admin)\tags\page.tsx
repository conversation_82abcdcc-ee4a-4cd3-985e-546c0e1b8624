'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  TagsTable,
  TagsFilters,
  TagDialog,
  TagCloud,
  TagAnalytics,
  BulkTagActions,
  TagMergeDialog,
} from '@/components/tags';
import { useTagsManagement } from '@/hooks/use-tags-management';
import {
  Plus,
  RefreshCw,
  AlertTriangle,
  Hash,
  BarChart3,
  Cloud,
  Table,
  Merge,
} from 'lucide-react';
import type { Tag } from '@/types';

interface TagWithStats extends Tag {
  post_count?: number;
  usage_trend?: 'up' | 'down' | 'stable';
}

export default function TagsPage() {
  const {
    tags,
    filteredTags,
    loading,
    errors,
    filters,
    updateFilters,
    resetFilters,
    refresh,
    createNewTag,
    updateExistingTag,
    deleteTags,
    duplicateTags,
    mergeTags,
    exportTags,
  } = useTagsManagement();

  const [selectedTags, setSelectedTags] = useState<TagWithStats[]>([]);
  const [showTagDialog, setShowTagDialog] = useState(false);
  const [showMergeDialog, setShowMergeDialog] = useState(false);
  const [editingTag, setEditingTag] = useState<TagWithStats | null>(null);
  const [activeTab, setActiveTab] = useState('cloud');

  // Handle tag actions
  const handleEdit = (tag: TagWithStats) => {
    setEditingTag(tag);
    setShowTagDialog(true);
  };

  const handleDelete = async (tag: TagWithStats) => {
    try {
      await deleteTags([tag]);
    } catch (error) {
      console.error('Failed to delete tag:', error);
    }
  };

  const handleDuplicate = async (tag: TagWithStats) => {
    try {
      await duplicateTags([tag]);
    } catch (error) {
      console.error('Failed to duplicate tag:', error);
    }
  };

  const handleViewPosts = (tag: TagWithStats) => {
    // Navigate to posts page with tag filter
    window.location.href = `/posts?tag=${tag.id}`;
  };

  const handleMerge = (tags: TagWithStats[]) => {
    setSelectedTags(tags);
    setShowMergeDialog(true);
  };

  const handleSaveTag = async (tagData: Partial<Tag>) => {
    if (editingTag?.id) {
      // Update existing tag
      return await updateExistingTag(editingTag.id, tagData);
    } else {
      // Create new tag
      return await createNewTag(tagData);
    }
  };

  const handleDialogClose = () => {
    setShowTagDialog(false);
    setEditingTag(null);
  };

  const handleMergeDialogClose = () => {
    setShowMergeDialog(false);
    setSelectedTags([]);
  };

  const handleBulkDelete = async (tagsToDelete: TagWithStats[]) => {
    try {
      await deleteTags(tagsToDelete);
      setSelectedTags([]);
    } catch (error) {
      console.error('Failed to delete tags:', error);
    }
  };

  const handleBulkDuplicate = async (tagsToDuplicate: TagWithStats[]) => {
    try {
      await duplicateTags(tagsToDuplicate);
      setSelectedTags([]);
    } catch (error) {
      console.error('Failed to duplicate tags:', error);
    }
  };

  const handleBulkMerge = async (tagsToMerge: TagWithStats[]) => {
    if (tagsToMerge.length < 2) return;
    setSelectedTags(tagsToMerge);
    setShowMergeDialog(true);
  };

  const handleBulkExport = async (tagsToExport: TagWithStats[]) => {
    try {
      await exportTags(tagsToExport);
    } catch (error) {
      console.error('Failed to export tags:', error);
    }
  };

  const handleMergeComplete = async (
    targetTag: TagWithStats,
    tagsToMerge: TagWithStats[]
  ) => {
    try {
      await mergeTags(targetTag, tagsToMerge);
      setSelectedTags([]);
      setShowMergeDialog(false);
    } catch (error) {
      console.error('Failed to merge tags:', error);
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Tags</h2>
          <p className="text-muted-foreground">
            Manage content tags for better discoverability and organization
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={loading.tags}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${loading.tags ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Button onClick={() => setShowTagDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Tag
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {(errors.tags || errors.actions) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{errors.tags || errors.actions}</AlertDescription>
        </Alert>
      )}

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Tags</CardTitle>
            <Hash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{tags.length}</div>
            <p className="text-xs text-muted-foreground">
              {tags.filter((t) => (t.post_count || 0) > 0).length} in use
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tags.reduce((sum, tag) => sum + (tag.post_count || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">Across all posts</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Filtered Results
            </CardTitle>
            <Table className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{filteredTags.length}</div>
            <p className="text-xs text-muted-foreground">
              Matching current filters
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Selected</CardTitle>
            <Merge className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{selectedTags.length}</div>
            <p className="text-xs text-muted-foreground">Tags selected</p>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      <BulkTagActions
        selectedTags={selectedTags}
        onDelete={handleBulkDelete}
        onDuplicate={handleBulkDuplicate}
        onMerge={handleBulkMerge}
        onExport={handleBulkExport}
        disabled={loading.actions}
      />

      {/* Main Content Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="cloud" className="flex items-center gap-2">
            <Cloud className="h-4 w-4" />
            Tag Cloud
          </TabsTrigger>
          <TabsTrigger value="table" className="flex items-center gap-2">
            <Table className="h-4 w-4" />
            Table View
            <Badge variant="secondary">{filteredTags.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Tag Cloud View */}
        <TabsContent value="cloud" className="space-y-4">
          <TagCloud
            tags={filteredTags}
            onTagClick={handleViewPosts}
            onTagEdit={handleEdit}
          />
        </TabsContent>

        {/* Table View */}
        <TabsContent value="table" className="space-y-4">
          <TagsFilters
            filters={filters}
            onFiltersChange={updateFilters}
            onReset={resetFilters}
            tags={tags}
            loading={loading.tags}
          />

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Hash className="h-5 w-5" />
                Tags
                <Badge variant="secondary">{filteredTags.length}</Badge>
              </CardTitle>
              <CardDescription>
                Manage your content tags with advanced filtering and bulk
                operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <TagsTable
                tags={filteredTags}
                isLoading={loading.tags}
                selectedTags={selectedTags}
                onSelectionChange={setSelectedTags}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onView={handleViewPosts}
                onDuplicate={handleDuplicate}
                onMerge={handleMerge}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics View */}
        <TabsContent value="analytics" className="space-y-4">
          <TagAnalytics tags={tags} />
        </TabsContent>
      </Tabs>

      {/* Tag Dialog */}
      <TagDialog
        isOpen={showTagDialog}
        onOpenChange={handleDialogClose}
        tag={editingTag}
        tags={tags}
        onSave={handleSaveTag}
        loading={loading.actions}
      />

      {/* Tag Merge Dialog */}
      <TagMergeDialog
        isOpen={showMergeDialog}
        onOpenChange={handleMergeDialogClose}
        tags={selectedTags}
        allTags={tags}
        onMerge={handleMergeComplete}
        loading={loading.actions}
      />
    </div>
  );
}
