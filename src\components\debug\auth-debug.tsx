'use client';

import { useAuth } from '@/contexts/auth-context';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { RefreshCw, User, Shield, AlertTriangle, CheckCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

export function AuthDebug() {
  const { 
    user, 
    profile, 
    loading, 
    error, 
    isAdmin, 
    isPublisher, 
    isEditor, 
    isMember,
    hasRole,
    refreshProfile 
  } = useAuth();

  const handleRefresh = async () => {
    try {
      await refreshProfile();
    } catch (err) {
      console.error('Failed to refresh profile:', err);
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Authentication Debug Panel
          </CardTitle>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleRefresh}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Loading State */}
          {loading && (
            <Alert>
              <RefreshCw className="h-4 w-4 animate-spin" />
              <AlertDescription>Loading authentication state...</AlertDescription>
            </Alert>
          )}

          {/* Error State */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>Error: {error}</AlertDescription>
            </Alert>
          )}

          {/* Authentication Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* User Info */}
            <div className="space-y-2">
              <h3 className="font-semibold flex items-center gap-2">
                <User className="h-4 w-4" />
                User Authentication
              </h3>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Status:</span>
                  {user ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Authenticated
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Not Authenticated
                    </Badge>
                  )}
                </div>
                {user && (
                  <>
                    <div><span className="font-medium">User ID:</span> {user.id}</div>
                    <div><span className="font-medium">Email:</span> {user.email}</div>
                    <div><span className="font-medium">Email Verified:</span> {user.email_confirmed_at ? 'Yes' : 'No'}</div>
                  </>
                )}
              </div>
            </div>

            {/* Profile Info */}
            <div className="space-y-2">
              <h3 className="font-semibold flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Profile & Permissions
              </h3>
              <div className="space-y-1 text-sm">
                <div className="flex items-center gap-2">
                  <span className="font-medium">Profile Status:</span>
                  {profile ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Loaded
                    </Badge>
                  ) : (
                    <Badge variant="destructive">
                      <AlertTriangle className="h-3 w-3 mr-1" />
                      Not Loaded
                    </Badge>
                  )}
                </div>
                {profile && (
                  <>
                    <div><span className="font-medium">Full Name:</span> {profile.full_name || 'Not set'}</div>
                    <div><span className="font-medium">Role:</span> 
                      <Badge variant="outline" className="ml-2">
                        {profile.role}
                      </Badge>
                    </div>
                    <div><span className="font-medium">Created:</span> {new Date(profile.created_at).toLocaleDateString()}</div>
                    <div><span className="font-medium">Updated:</span> {new Date(profile.updated_at).toLocaleDateString()}</div>
                  </>
                )}
              </div>
            </div>
          </div>

          {/* Role Permissions */}
          {profile && (
            <div className="space-y-2">
              <h3 className="font-semibold">Role Permissions</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                <div className="flex items-center gap-2">
                  <span className="text-sm">Admin:</span>
                  {isAdmin ? (
                    <Badge variant="default" className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Yes
                    </Badge>
                  ) : (
                    <Badge variant="secondary">No</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">Publisher:</span>
                  {isPublisher ? (
                    <Badge variant="default" className="bg-blue-100 text-blue-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Yes
                    </Badge>
                  ) : (
                    <Badge variant="secondary">No</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">Editor:</span>
                  {isEditor ? (
                    <Badge variant="default" className="bg-yellow-100 text-yellow-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Yes
                    </Badge>
                  ) : (
                    <Badge variant="secondary">No</Badge>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm">Member:</span>
                  {isMember ? (
                    <Badge variant="default" className="bg-gray-100 text-gray-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Yes
                    </Badge>
                  ) : (
                    <Badge variant="secondary">No</Badge>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Raw Data (for debugging) */}
          <details className="space-y-2">
            <summary className="font-semibold cursor-pointer">Raw Data (Debug)</summary>
            <div className="space-y-2 text-xs">
              <div>
                <span className="font-medium">User Object:</span>
                <pre className="bg-gray-100 p-2 rounded mt-1 overflow-auto">
                  {JSON.stringify(user, null, 2)}
                </pre>
              </div>
              <div>
                <span className="font-medium">Profile Object:</span>
                <pre className="bg-gray-100 p-2 rounded mt-1 overflow-auto">
                  {JSON.stringify(profile, null, 2)}
                </pre>
              </div>
            </div>
          </details>
        </CardContent>
      </Card>
    </div>
  );
}
