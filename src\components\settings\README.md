# Settings Management Components

This directory contains the comprehensive Settings Management functionality for the FSNC Dashboard application.

## Overview

The Settings Management system provides a centralized interface for configuring organization-wide settings, content policies, user access controls, system monitoring, and analytics configuration.

## Components

### Main Settings Page
- **Location**: `src/app/(admin)/settings/page.tsx`
- **Route**: `/settings`
- **Access**: Admin users only
- **Features**: Tab-based interface with real-time save functionality

### Settings Components

#### 1. OrganizationConfig.tsx
**Organization Configuration Hub**
- Organization branding and basic information
- Logo and favicon management
- Social media links configuration
- Newsletter settings

#### 2. ContentPolicies.tsx
**Content Management Policies**
- Content approval workflows
- Publishing schedules and automation
- Content retention and archival policies
- SEO defaults and metadata templates
- Content quality standards

#### 3. UserAccessManagement.tsx
**User and Access Management**
- Default roles and permissions
- Registration and invitation policies
- Session timeout and security settings
- API access and rate limiting
- Integration and webhook configuration

#### 4. SystemMonitoring.tsx
**System Monitoring and Maintenance**
- Real-time system health overview
- Performance monitoring configuration
- Database optimization tools
- Backup and restore management
- Error logging and debugging
- Maintenance scheduling

#### 5. AnalyticsConfig.tsx
**Analytics and Reporting Configuration**
- Analytics tracking setup (Google Analytics, GTM)
- Custom report generation
- Data retention and privacy settings
- Export and integration configuration
- Performance monitoring thresholds

## Hook

### useSettingsManagement
**Location**: `src/hooks/use-settings-management.ts`

**Features**:
- Load settings from database
- Track unsaved changes
- Bulk save functionality
- Error handling and loading states
- Real-time updates

**Usage**:
```typescript
const {
  settings,
  loading,
  errors,
  hasUnsavedChanges,
  updateSetting,
  saveSettings,
  refresh,
} = useSettingsManagement();
```

## Database Integration

### Organization Settings Table
The settings are stored in the `organization_settings` table with the following structure:
- `key` (TEXT, PRIMARY KEY): Setting identifier
- `value` (JSONB): Setting value (flexible JSON storage)
- `description` (TEXT): Human-readable description
- `updated_at` (TIMESTAMPTZ): Last update timestamp

### Database Helpers
**Location**: `src/lib/database-helpers.ts`

**Functions**:
- `getOrganizationSettings()`: Fetch all settings
- `getOrganizationSetting(key)`: Fetch specific setting
- `upsertOrganizationSetting(key, value, description)`: Update/create setting
- `bulkUpdateOrganizationSettings(settings)`: Bulk update multiple settings

## Features

### Real-time Updates
- Changes are tracked locally before saving
- Visual indicators for unsaved changes
- Bulk save functionality for performance

### Form Validation
- Type-safe form handling
- Input validation and error display
- Consistent UI patterns using Shadcn/UI

### Security
- Admin-only access through RLS policies
- Secure handling of sensitive configuration data
- Audit trail through database triggers

### User Experience
- Tab-based organization for easy navigation
- Responsive design for all screen sizes
- Loading states and error handling
- Auto-save indicators and manual save options

## Usage Examples

### Updating Organization Name
```typescript
// In any settings component
onUpdateSetting('organization_name', 'New Organization Name');
```

### Configuring Content Workflow
```typescript
// Enable content approval
const currentWorkflow = settings.content_workflow || {};
onUpdateSetting('content_workflow', {
  ...currentWorkflow,
  require_approval: true,
  approval_roles: 'admin_publisher'
});
```

### Setting Up Analytics
```typescript
// Configure Google Analytics
onUpdateSetting('analytics_tracking', {
  enabled: true,
  google_analytics_id: 'G-XXXXXXXXXX',
  track_page_views: true,
  track_user_interactions: true
});
```

## Styling

All components use the established Shadcn/UI design system with:
- Consistent spacing and typography
- Proper color schemes and contrast
- Responsive grid layouts
- Accessible form controls

## Future Enhancements

Potential areas for expansion:
- Import/export settings functionality
- Settings versioning and rollback
- Advanced permission granularity
- Integration with external configuration management
- Real-time collaboration on settings changes
