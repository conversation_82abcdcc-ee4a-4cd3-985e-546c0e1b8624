'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  User, 
  Shield, 
  AlertTriangle, 
  CheckCircle,
  Database,
  Globe
} from 'lucide-react';
import { useState, useEffect } from 'react';
import { createClient } from '@/lib/client';

export default function DebugPage() {
  const [tests, setTests] = useState({
    supabaseConnection: { status: 'pending', message: '', data: null },
    userSession: { status: 'pending', message: '', data: null },
    profileAccess: { status: 'pending', message: '', data: null },
    rlsPolicies: { status: 'pending', message: '', data: null },
  });

  const [loading, setLoading] = useState(false);

  const runTests = async () => {
    setLoading(true);
    const supabase = createClient();
    const newTests = { ...tests };

    try {
      // Test 1: Supabase Connection
      console.log('🔍 Testing Supabase connection...');
      try {
        const { data, error } = await supabase.from('profiles').select('count').limit(1);
        if (error) throw error;
        newTests.supabaseConnection = {
          status: 'success',
          message: 'Successfully connected to Supabase',
          data: 'Connection OK'
        };
      } catch (err) {
        newTests.supabaseConnection = {
          status: 'error',
          message: `Connection failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
          data: null
        };
      }

      // Test 2: User Session
      console.log('🔍 Testing user session...');
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        if (error) throw error;
        
        if (session?.user) {
          newTests.userSession = {
            status: 'success',
            message: `User authenticated: ${session.user.email}`,
            data: {
              id: session.user.id,
              email: session.user.email,
              emailConfirmed: !!session.user.email_confirmed_at,
              lastSignIn: session.user.last_sign_in_at
            }
          };
        } else {
          newTests.userSession = {
            status: 'warning',
            message: 'No active user session found',
            data: null
          };
        }
      } catch (err) {
        newTests.userSession = {
          status: 'error',
          message: `Session check failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
          data: null
        };
      }

      // Test 3: Profile Access (only if user exists)
      if (newTests.userSession.status === 'success' && newTests.userSession.data) {
        console.log('🔍 Testing profile access...');
        try {
          const { data: profile, error } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', newTests.userSession.data.id)
            .is('deleted_at', null)
            .single();

          if (error) throw error;

          newTests.profileAccess = {
            status: 'success',
            message: `Profile loaded successfully`,
            data: {
              id: profile.id,
              email: profile.email,
              fullName: profile.full_name,
              role: profile.role,
              createdAt: profile.created_at,
              updatedAt: profile.updated_at
            }
          };
        } catch (err) {
          newTests.profileAccess = {
            status: 'error',
            message: `Profile access failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
            data: null
          };
        }

        // Test 4: RLS Policies (only if profile exists)
        if (newTests.profileAccess.status === 'success') {
          console.log('🔍 Testing RLS policies...');
          try {
            const { data: roleCheck, error } = await supabase
              .rpc('user_has_role', {
                user_id: newTests.userSession.data.id,
                required_role: 'admin'
              });

            if (error) throw error;

            newTests.rlsPolicies = {
              status: 'success',
              message: `RLS function works: user_has_role('admin') = ${roleCheck}`,
              data: { isAdmin: roleCheck }
            };
          } catch (err) {
            newTests.rlsPolicies = {
              status: 'error',
              message: `RLS test failed: ${err instanceof Error ? err.message : 'Unknown error'}`,
              data: null
            };
          }
        }
      }

    } catch (err) {
      console.error('❌ Unexpected error during tests:', err);
    }

    setTests(newTests);
    setLoading(false);
  };

  useEffect(() => {
    runTests();
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      default: return <RefreshCw className="h-4 w-4 animate-spin" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-6 p-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Debug Dashboard</h1>
          <p className="text-muted-foreground">
            Troubleshooting authentication and database connectivity
          </p>
        </div>
        <Button onClick={runTests} disabled={loading}>
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Run Tests
        </Button>
      </div>

      <div className="grid gap-4">
        {/* Test Results */}
        {Object.entries(tests).map(([testName, result]) => (
          <Card key={testName}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-base font-medium flex items-center gap-2">
                {testName === 'supabaseConnection' && <Database className="h-4 w-4" />}
                {testName === 'userSession' && <User className="h-4 w-4" />}
                {testName === 'profileAccess' && <Shield className="h-4 w-4" />}
                {testName === 'rlsPolicies' && <Globe className="h-4 w-4" />}
                {testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
              </CardTitle>
              <Badge className={getStatusColor(result.status)}>
                {getStatusIcon(result.status)}
                {result.status}
              </Badge>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
              {result.data && (
                <details className="text-xs">
                  <summary className="cursor-pointer font-medium">View Data</summary>
                  <pre className="bg-gray-100 p-2 rounded mt-2 overflow-auto">
                    {JSON.stringify(result.data, null, 2)}
                  </pre>
                </details>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Environment Info */}
      <Card>
        <CardHeader>
          <CardTitle>Environment Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Supabase URL:</span> 
              <span className="ml-2">{process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing'}</span>
            </div>
            <div>
              <span className="font-medium">Supabase Anon Key:</span> 
              <span className="ml-2">{process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}</span>
            </div>
            <div>
              <span className="font-medium">Current URL:</span> 
              <span className="ml-2">{typeof window !== 'undefined' ? window.location.href : 'Server-side'}</span>
            </div>
            <div>
              <span className="font-medium">User Agent:</span> 
              <span className="ml-2">{typeof window !== 'undefined' ? navigator.userAgent.substring(0, 50) + '...' : 'Server-side'}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
