'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  getOrganizationSettings,
  bulkUpdateOrganizationSettings,
} from '@/lib/database-helpers';
import type { OrganizationSettings } from '@/types';

interface SettingsState {
  [key: string]: any;
}

interface LoadingState {
  settings: boolean;
  saving: boolean;
}

interface ErrorState {
  general?: string;
  [key: string]: string | undefined;
}

interface SettingsManagementReturn {
  settings: SettingsState;
  loading: LoadingState;
  errors: ErrorState;
  hasUnsavedChanges: boolean;
  updateSetting: (key: string, value: any) => void;
  saveSettings: () => Promise<{ success: boolean; error?: string }>;
  refresh: () => Promise<void>;
}

export function useSettingsManagement(): SettingsManagementReturn {
  const [settings, setSettings] = useState<SettingsState>({});
  const [originalSettings, setOriginalSettings] = useState<SettingsState>({});
  const [loading, setLoading] = useState<LoadingState>({
    settings: true,
    saving: false,
  });
  const [errors, setErrors] = useState<ErrorState>({});

  // Load settings from database
  const loadSettings = useCallback(async () => {
    setLoading((prev) => ({ ...prev, settings: true }));
    setErrors({});

    try {
      const data = await getOrganizationSettings();

      // Convert array of settings to object
      const settingsObject: SettingsState = {};
      data.forEach((setting: OrganizationSettings) => {
        settingsObject[setting.key] = setting.value;
      });

      setSettings(settingsObject);
      setOriginalSettings(settingsObject);
    } catch (error) {
      setErrors({
        general:
          error instanceof Error ? error.message : 'Failed to load settings',
      });
    } finally {
      setLoading((prev) => ({ ...prev, settings: false }));
    }
  }, []);

  // Update a setting value locally
  const updateSetting = useCallback((key: string, value: any) => {
    setSettings((prev) => ({
      ...prev,
      [key]: value,
    }));
  }, []);

  // Save all settings to database
  const saveSettings = useCallback(async (): Promise<{
    success: boolean;
    error?: string;
  }> => {
    setLoading((prev) => ({ ...prev, saving: true }));
    setErrors({});

    try {
      // Find changed settings
      const changedSettings = Object.keys(settings).filter(
        (key) =>
          JSON.stringify(settings[key]) !==
          JSON.stringify(originalSettings[key])
      );

      if (changedSettings.length === 0) {
        return { success: true };
      }

      // Prepare upsert data
      const upsertData = changedSettings.map((key) => ({
        key,
        value: settings[key],
      }));

      await bulkUpdateOrganizationSettings(upsertData);

      // Update original settings to reflect saved state
      setOriginalSettings({ ...settings });

      return { success: true };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : 'Failed to save settings';
      setErrors({ general: errorMessage });
      return { success: false, error: errorMessage };
    } finally {
      setLoading((prev) => ({ ...prev, saving: false }));
    }
  }, [settings, originalSettings]);

  // Refresh settings from database
  const refresh = useCallback(async () => {
    await loadSettings();
  }, [loadSettings]);

  // Check if there are unsaved changes
  const hasUnsavedChanges = Object.keys(settings).some(
    (key) =>
      JSON.stringify(settings[key]) !== JSON.stringify(originalSettings[key])
  );

  // Load settings on mount
  useEffect(() => {
    loadSettings();
  }, [loadSettings]);

  return {
    settings,
    loading,
    errors,
    hasUnsavedChanges,
    updateSetting,
    saveSettings,
    refresh,
  };
}
