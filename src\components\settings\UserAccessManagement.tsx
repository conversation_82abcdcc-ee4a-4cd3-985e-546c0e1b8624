'use client';

import { <PERSON>, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { 
  Users, 
  Shield, 
  Clock, 
  Key, 
  Webhook,
  UserPlus,
  Settings,
  AlertTriangle
} from 'lucide-react';

interface UserAccessManagementProps {
  settings: Record<string, any>;
  loading: { settings: boolean; saving: boolean };
  errors: Record<string, string | undefined>;
  onUpdateSetting: (key: string, value: any) => void;
}

export function UserAccessManagement({ 
  settings, 
  loading, 
  errors, 
  onUpdateSetting 
}: UserAccessManagementProps) {
  const handleRoleDefaultsChange = (key: string, value: any) => {
    const currentDefaults = settings.role_defaults || {};
    onUpdateSetting('role_defaults', {
      ...currentDefaults,
      [key]: value,
    });
  };

  const handleRegistrationChange = (key: string, value: any) => {
    const currentRegistration = settings.registration_policies || {};
    onUpdateSetting('registration_policies', {
      ...currentRegistration,
      [key]: value,
    });
  };

  const handleSecurityChange = (key: string, value: any) => {
    const currentSecurity = settings.security_settings || {};
    onUpdateSetting('security_settings', {
      ...currentSecurity,
      [key]: value,
    });
  };

  const handleApiAccessChange = (key: string, value: any) => {
    const currentApi = settings.api_access || {};
    onUpdateSetting('api_access', {
      ...currentApi,
      [key]: value,
    });
  };

  const handleIntegrationChange = (key: string, value: any) => {
    const currentIntegrations = settings.integrations || {};
    onUpdateSetting('integrations', {
      ...currentIntegrations,
      [key]: value,
    });
  };

  return (
    <div className="space-y-6">
      {/* Default User Roles & Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Default User Roles & Permissions
          </CardTitle>
          <CardDescription>
            Configure default roles and permissions for new users
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="default-role">Default Role for New Users</Label>
            <Select
              value={settings.role_defaults?.default_role || 'member'}
              onValueChange={(value) => handleRoleDefaultsChange('default_role', value)}
              disabled={loading.saving}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select default role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="member">Member</SelectItem>
                <SelectItem value="editor">Editor</SelectItem>
                <SelectItem value="publisher">Publisher</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-3">
            <Label>Role Permissions Overview</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="p-3 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="secondary">Member</Badge>
                  <span className="text-xs text-muted-foreground">Read Only</span>
                </div>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• View published content</li>
                  <li>• Access dashboard</li>
                  <li>• Update own profile</li>
                </ul>
              </div>
              <div className="p-3 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="secondary">Editor</Badge>
                  <span className="text-xs text-muted-foreground">Content Creation</span>
                </div>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Create and edit drafts</li>
                  <li>• Manage own content</li>
                  <li>• Upload media</li>
                </ul>
              </div>
              <div className="p-3 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="secondary">Publisher</Badge>
                  <span className="text-xs text-muted-foreground">Content Management</span>
                </div>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Publish content</li>
                  <li>• Manage categories/tags</li>
                  <li>• Review submissions</li>
                </ul>
              </div>
              <div className="p-3 border rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <Badge variant="destructive">Admin</Badge>
                  <span className="text-xs text-muted-foreground">Full Access</span>
                </div>
                <ul className="text-xs text-muted-foreground space-y-1">
                  <li>• Full system access</li>
                  <li>• User management</li>
                  <li>• System settings</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Registration & Invitation Policies */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Registration & Invitation Policies
          </CardTitle>
          <CardDescription>
            Configure how new users can join your organization
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Invitation-Only Registration</Label>
              <p className="text-sm text-muted-foreground">
                Users can only join through invitations
              </p>
            </div>
            <Switch
              checked={settings.registration_policies?.invitation_only || true}
              onCheckedChange={(checked) => handleRegistrationChange('invitation_only', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="invitation-expiry">Invitation Expiry (days)</Label>
              <Input
                id="invitation-expiry"
                type="number"
                value={settings.registration_policies?.invitation_expiry_days || 7}
                onChange={(e) => handleRegistrationChange('invitation_expiry_days', parseInt(e.target.value))}
                disabled={loading.saving}
                min="1"
                max="30"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="max-resends">Max Invitation Resends</Label>
              <Input
                id="max-resends"
                type="number"
                value={settings.registration_policies?.max_resends || 3}
                onChange={(e) => handleRegistrationChange('max_resends', parseInt(e.target.value))}
                disabled={loading.saving}
                min="1"
                max="10"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Require Email Verification</Label>
              <p className="text-sm text-muted-foreground">
                Users must verify their email before accessing the system
              </p>
            </div>
            <Switch
              checked={settings.registration_policies?.require_email_verification || true}
              onCheckedChange={(checked) => handleRegistrationChange('require_email_verification', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="welcome-message">Welcome Message Template</Label>
            <Textarea
              id="welcome-message"
              value={settings.registration_policies?.welcome_message || 'Welcome to our organization! We\'re excited to have you on board.'}
              onChange={(e) => handleRegistrationChange('welcome_message', e.target.value)}
              placeholder="Welcome message for new users"
              rows={3}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Session Timeout & Security */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Session Timeout & Security
          </CardTitle>
          <CardDescription>
            Configure session management and security settings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="session-timeout">Session Timeout (minutes)</Label>
              <Input
                id="session-timeout"
                type="number"
                value={settings.security_settings?.session_timeout_minutes || 480}
                onChange={(e) => handleSecurityChange('session_timeout_minutes', parseInt(e.target.value))}
                disabled={loading.saving}
                min="15"
                max="1440"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="max-login-attempts">Max Login Attempts</Label>
              <Input
                id="max-login-attempts"
                type="number"
                value={settings.security_settings?.max_login_attempts || 5}
                onChange={(e) => handleSecurityChange('max_login_attempts', parseInt(e.target.value))}
                disabled={loading.saving}
                min="3"
                max="10"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Force Password Reset on First Login</Label>
              <p className="text-sm text-muted-foreground">
                Require users to change password on first login
              </p>
            </div>
            <Switch
              checked={settings.security_settings?.force_password_reset || false}
              onCheckedChange={(checked) => handleSecurityChange('force_password_reset', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Two-Factor Authentication</Label>
              <p className="text-sm text-muted-foreground">
                Allow users to enable 2FA for their accounts
              </p>
            </div>
            <Switch
              checked={settings.security_settings?.enable_2fa || false}
              onCheckedChange={(checked) => handleSecurityChange('enable_2fa', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* API Access & Rate Limiting */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            API Access & Rate Limiting
          </CardTitle>
          <CardDescription>
            Configure API access permissions and rate limiting
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable API Access</Label>
              <p className="text-sm text-muted-foreground">
                Allow external applications to access your data via API
              </p>
            </div>
            <Switch
              checked={settings.api_access?.enabled || false}
              onCheckedChange={(checked) => handleApiAccessChange('enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.api_access?.enabled && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="rate-limit">Rate Limit (requests/minute)</Label>
                  <Input
                    id="rate-limit"
                    type="number"
                    value={settings.api_access?.rate_limit_per_minute || 60}
                    onChange={(e) => handleApiAccessChange('rate_limit_per_minute', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="10"
                    max="1000"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="api-key-expiry">API Key Expiry (days)</Label>
                  <Input
                    id="api-key-expiry"
                    type="number"
                    value={settings.api_access?.key_expiry_days || 365}
                    onChange={(e) => handleApiAccessChange('key_expiry_days', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="30"
                    max="3650"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Require API Key for Read Operations</Label>
                  <p className="text-sm text-muted-foreground">
                    Require authentication even for reading public data
                  </p>
                </div>
                <Switch
                  checked={settings.api_access?.require_key_for_read || false}
                  onCheckedChange={(checked) => handleApiAccessChange('require_key_for_read', checked)}
                  disabled={loading.saving}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Integration & Webhook Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Webhook className="h-5 w-5" />
            Integration & Webhook Configuration
          </CardTitle>
          <CardDescription>
            Configure external integrations and webhook endpoints
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Webhooks</Label>
              <p className="text-sm text-muted-foreground">
                Send notifications to external services when events occur
              </p>
            </div>
            <Switch
              checked={settings.integrations?.webhooks_enabled || false}
              onCheckedChange={(checked) => handleIntegrationChange('webhooks_enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.integrations?.webhooks_enabled && (
            <>
              <div className="space-y-2">
                <Label htmlFor="webhook-url">Default Webhook URL</Label>
                <Input
                  id="webhook-url"
                  type="url"
                  value={settings.integrations?.default_webhook_url || ''}
                  onChange={(e) => handleIntegrationChange('default_webhook_url', e.target.value)}
                  placeholder="https://your-service.com/webhook"
                  disabled={loading.saving}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="webhook-secret">Webhook Secret</Label>
                <Input
                  id="webhook-secret"
                  type="password"
                  value={settings.integrations?.webhook_secret || ''}
                  onChange={(e) => handleIntegrationChange('webhook_secret', e.target.value)}
                  placeholder="Secret for webhook verification"
                  disabled={loading.saving}
                />
              </div>
            </>
          )}

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Third-party Integrations</Label>
              <p className="text-sm text-muted-foreground">
                Allow connections to external services and platforms
              </p>
            </div>
            <Switch
              checked={settings.integrations?.third_party_enabled || false}
              onCheckedChange={(checked) => handleIntegrationChange('third_party_enabled', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
