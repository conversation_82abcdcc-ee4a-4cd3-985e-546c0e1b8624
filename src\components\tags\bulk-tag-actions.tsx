'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import {
  MoreHorizontal,
  Trash2,
  Copy,
  Merge,
  Download,
  Loader2,
} from 'lucide-react';
import type { Tag } from '@/types';

interface TagWithStats extends Tag {
  post_count?: number;
  usage_trend?: 'up' | 'down' | 'stable';
}

interface BulkTagActionsProps {
  selectedTags: TagWithStats[];
  onDelete?: (tags: TagWithStats[]) => Promise<void>;
  onDuplicate?: (tags: TagWithStats[]) => Promise<void>;
  onMerge?: (tags: TagWithStats[]) => Promise<void>;
  onExport?: (tags: TagWithStats[]) => Promise<void>;
  disabled?: boolean;
}

export function BulkTagActions({
  selectedTags,
  onDelete,
  onDuplicate,
  onMerge,
  onExport,
  disabled = false,
}: BulkTagActionsProps) {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingAction, setLoadingAction] = useState<string | null>(null);

  const selectedCount = selectedTags.length;

  if (selectedCount === 0) {
    return null;
  }

  const handleAction = async (
    action: () => Promise<void>,
    actionName: string
  ) => {
    setIsLoading(true);
    setLoadingAction(actionName);
    try {
      await action();
    } catch (error) {
      console.error(`Failed to ${actionName}:`, error);
    } finally {
      setIsLoading(false);
      setLoadingAction(null);
    }
  };

  const handleDelete = async () => {
    if (onDelete) {
      await handleAction(
        () => onDelete(selectedTags),
        'delete tags'
      );
    }
    setShowDeleteDialog(false);
  };

  const handleDuplicate = async () => {
    if (onDuplicate) {
      await handleAction(
        () => onDuplicate(selectedTags),
        'duplicate tags'
      );
    }
  };

  const handleMerge = async () => {
    if (onMerge) {
      await handleAction(
        () => onMerge(selectedTags),
        'merge tags'
      );
    }
  };

  const handleExport = async () => {
    if (onExport) {
      await handleAction(
        () => onExport(selectedTags),
        'export tags'
      );
    }
  };

  return (
    <>
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="px-2 py-1">
          {selectedCount} selected
        </Badge>
        
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              disabled={disabled || isLoading}
              className="h-8"
            >
              {isLoading && loadingAction && (
                <Loader2 className="mr-2 h-3 w-3 animate-spin" />
              )}
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Bulk actions</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>Bulk Actions</DropdownMenuLabel>
            <DropdownMenuSeparator />
            
            {onDuplicate && (
              <DropdownMenuItem
                onClick={handleDuplicate}
                disabled={isLoading}
              >
                <Copy className="mr-2 h-4 w-4" />
                Duplicate ({selectedCount})
              </DropdownMenuItem>
            )}
            
            {onMerge && selectedCount > 1 && (
              <DropdownMenuItem
                onClick={handleMerge}
                disabled={isLoading}
              >
                <Merge className="mr-2 h-4 w-4" />
                Merge ({selectedCount})
              </DropdownMenuItem>
            )}
            
            {onExport && (
              <DropdownMenuItem
                onClick={handleExport}
                disabled={isLoading}
              >
                <Download className="mr-2 h-4 w-4" />
                Export ({selectedCount})
              </DropdownMenuItem>
            )}
            
            <DropdownMenuSeparator />
            
            {onDelete && (
              <DropdownMenuItem
                onClick={() => setShowDeleteDialog(true)}
                disabled={isLoading}
                className="text-destructive focus:text-destructive"
              >
                <Trash2 className="mr-2 h-4 w-4" />
                Delete ({selectedCount})
              </DropdownMenuItem>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Tags</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedCount} tag{selectedCount === 1 ? '' : 's'}?
              This action cannot be undone.
              {selectedTags.some(tag => (tag.post_count || 0) > 0) && (
                <div className="mt-2 p-2 bg-yellow-50 border border-yellow-200 rounded text-yellow-800 text-sm">
                  <strong>Warning:</strong> Some tags are being used by posts. 
                  Deleting them will remove the tag association from those posts.
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isLoading}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isLoading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isLoading && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              Delete {selectedCount} Tag{selectedCount === 1 ? '' : 's'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
