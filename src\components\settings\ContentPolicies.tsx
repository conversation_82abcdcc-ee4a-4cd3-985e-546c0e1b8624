'use client';

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  FileText, 
  Clock, 
  Shield, 
  Search, 
  Archive,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

interface ContentPoliciesProps {
  settings: Record<string, any>;
  loading: { settings: boolean; saving: boolean };
  errors: Record<string, string | undefined>;
  onUpdateSetting: (key: string, value: any) => void;
}

export function ContentPolicies({ 
  settings, 
  loading, 
  errors, 
  onUpdateSetting 
}: ContentPoliciesProps) {
  const handleWorkflowChange = (key: string, value: any) => {
    const currentWorkflow = settings.content_workflow || {};
    onUpdateSetting('content_workflow', {
      ...currentWorkflow,
      [key]: value,
    });
  };

  const handleRetentionChange = (key: string, value: any) => {
    const currentRetention = settings.content_retention || {};
    onUpdateSetting('content_retention', {
      ...currentRetention,
      [key]: value,
    });
  };

  const handleSeoDefaultsChange = (key: string, value: any) => {
    const currentSeo = settings.seo_defaults || {};
    onUpdateSetting('seo_defaults', {
      ...currentSeo,
      [key]: value,
    });
  };

  const handleQualityStandardsChange = (key: string, value: any) => {
    const currentStandards = settings.quality_standards || {};
    onUpdateSetting('quality_standards', {
      ...currentStandards,
      [key]: value,
    });
  };

  return (
    <div className="space-y-6">
      {/* Content Approval Workflow */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Content Approval Workflow
          </CardTitle>
          <CardDescription>
            Configure how content is reviewed and approved before publication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Require Approval for Posts</Label>
              <p className="text-sm text-muted-foreground">
                All posts must be approved before publishing
              </p>
            </div>
            <Switch
              checked={settings.content_workflow?.require_approval || false}
              onCheckedChange={(checked) => handleWorkflowChange('require_approval', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Require Approval for Pages</Label>
              <p className="text-sm text-muted-foreground">
                All pages must be approved before publishing
              </p>
            </div>
            <Switch
              checked={settings.content_workflow?.require_page_approval || false}
              onCheckedChange={(checked) => handleWorkflowChange('require_page_approval', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="approval-roles">Who Can Approve Content</Label>
            <Select
              value={settings.content_workflow?.approval_roles || 'admin'}
              onValueChange={(value) => handleWorkflowChange('approval_roles', value)}
              disabled={loading.saving}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select approval roles" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="admin">Admins Only</SelectItem>
                <SelectItem value="admin_publisher">Admins & Publishers</SelectItem>
                <SelectItem value="admin_publisher_editor">Admins, Publishers & Editors</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Auto-publish Scheduled Content</Label>
              <p className="text-sm text-muted-foreground">
                Automatically publish approved content at scheduled time
              </p>
            </div>
            <Switch
              checked={settings.content_workflow?.auto_publish_scheduled || true}
              onCheckedChange={(checked) => handleWorkflowChange('auto_publish_scheduled', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Publishing Schedules */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Publishing Schedules & Automation
          </CardTitle>
          <CardDescription>
            Configure automated publishing and scheduling rules
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="default-publish-time">Default Publish Time</Label>
              <Input
                id="default-publish-time"
                type="time"
                value={settings.content_workflow?.default_publish_time || '09:00'}
                onChange={(e) => handleWorkflowChange('default_publish_time', e.target.value)}
                disabled={loading.saving}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="timezone">Timezone</Label>
              <Select
                value={settings.content_workflow?.timezone || 'UTC'}
                onValueChange={(value) => handleWorkflowChange('timezone', value)}
                disabled={loading.saving}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="UTC">UTC</SelectItem>
                  <SelectItem value="America/New_York">Eastern Time</SelectItem>
                  <SelectItem value="America/Chicago">Central Time</SelectItem>
                  <SelectItem value="America/Denver">Mountain Time</SelectItem>
                  <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                  <SelectItem value="Europe/London">London</SelectItem>
                  <SelectItem value="Europe/Paris">Paris</SelectItem>
                  <SelectItem value="Asia/Tokyo">Tokyo</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Content Scheduling</Label>
              <p className="text-sm text-muted-foreground">
                Allow content to be scheduled for future publication
              </p>
            </div>
            <Switch
              checked={settings.content_workflow?.enable_scheduling || true}
              onCheckedChange={(checked) => handleWorkflowChange('enable_scheduling', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Content Retention & Archival */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Archive className="h-5 w-5" />
            Content Retention & Archival
          </CardTitle>
          <CardDescription>
            Configure how long content is kept and archival policies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Auto-archival</Label>
              <p className="text-sm text-muted-foreground">
                Automatically archive old content based on rules
              </p>
            </div>
            <Switch
              checked={settings.content_retention?.enable_auto_archive || false}
              onCheckedChange={(checked) => handleRetentionChange('enable_auto_archive', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.content_retention?.enable_auto_archive && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="archive-after-days">Archive Posts After (days)</Label>
                <Input
                  id="archive-after-days"
                  type="number"
                  value={settings.content_retention?.archive_after_days || 365}
                  onChange={(e) => handleRetentionChange('archive_after_days', parseInt(e.target.value))}
                  disabled={loading.saving}
                  min="1"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="delete-after-days">Delete Archived Content After (days)</Label>
                <Input
                  id="delete-after-days"
                  type="number"
                  value={settings.content_retention?.delete_after_days || 1095}
                  onChange={(e) => handleRetentionChange('delete_after_days', parseInt(e.target.value))}
                  disabled={loading.saving}
                  min="1"
                />
              </div>
            </div>
          )}

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Keep Draft Versions</Label>
              <p className="text-sm text-muted-foreground">
                Retain draft versions of published content
              </p>
            </div>
            <Switch
              checked={settings.content_retention?.keep_draft_versions || true}
              onCheckedChange={(checked) => handleRetentionChange('keep_draft_versions', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* SEO & Metadata Defaults */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            SEO & Metadata Defaults
          </CardTitle>
          <CardDescription>
            Configure default SEO settings and metadata for content
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="default-meta-title">Default Meta Title Template</Label>
            <Input
              id="default-meta-title"
              value={settings.seo_defaults?.meta_title_template || '{title} | {site_name}'}
              onChange={(e) => handleSeoDefaultsChange('meta_title_template', e.target.value)}
              placeholder="{title} | {site_name}"
              disabled={loading.saving}
            />
            <p className="text-xs text-muted-foreground">
              Use {'{title}'} for content title, {'{site_name}'} for organization name
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="default-meta-description">Default Meta Description Template</Label>
            <Textarea
              id="default-meta-description"
              value={settings.seo_defaults?.meta_description_template || ''}
              onChange={(e) => handleSeoDefaultsChange('meta_description_template', e.target.value)}
              placeholder="Default description for content without custom meta description"
              rows={2}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Auto-generate Meta Descriptions</Label>
              <p className="text-sm text-muted-foreground">
                Generate meta descriptions from content excerpt
              </p>
            </div>
            <Switch
              checked={settings.seo_defaults?.auto_generate_meta || true}
              onCheckedChange={(checked) => handleSeoDefaultsChange('auto_generate_meta', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Include Author in Meta Tags</Label>
              <p className="text-sm text-muted-foreground">
                Add author information to meta tags
              </p>
            </div>
            <Switch
              checked={settings.seo_defaults?.include_author_meta || true}
              onCheckedChange={(checked) => handleSeoDefaultsChange('include_author_meta', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Content Quality Standards */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Content Quality Standards
          </CardTitle>
          <CardDescription>
            Set guidelines and requirements for content quality
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="min-word-count">Minimum Word Count</Label>
              <Input
                id="min-word-count"
                type="number"
                value={settings.quality_standards?.min_word_count || 100}
                onChange={(e) => handleQualityStandardsChange('min_word_count', parseInt(e.target.value))}
                disabled={loading.saving}
                min="0"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="max-word-count">Maximum Word Count</Label>
              <Input
                id="max-word-count"
                type="number"
                value={settings.quality_standards?.max_word_count || 5000}
                onChange={(e) => handleQualityStandardsChange('max_word_count', parseInt(e.target.value))}
                disabled={loading.saving}
                min="0"
              />
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Require Featured Image</Label>
              <p className="text-sm text-muted-foreground">
                All posts must have a featured image
              </p>
            </div>
            <Switch
              checked={settings.quality_standards?.require_featured_image || false}
              onCheckedChange={(checked) => handleQualityStandardsChange('require_featured_image', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Require Excerpt</Label>
              <p className="text-sm text-muted-foreground">
                All posts must have an excerpt
              </p>
            </div>
            <Switch
              checked={settings.quality_standards?.require_excerpt || false}
              onCheckedChange={(checked) => handleQualityStandardsChange('require_excerpt', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Require Categories</Label>
              <p className="text-sm text-muted-foreground">
                All posts must be assigned to at least one category
              </p>
            </div>
            <Switch
              checked={settings.quality_standards?.require_categories || true}
              onCheckedChange={(checked) => handleQualityStandardsChange('require_categories', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
