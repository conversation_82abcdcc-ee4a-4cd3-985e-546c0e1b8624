'use client';

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  Download, 
  Calendar as CalendarIcon,
  Clock,
  Shield,
  Database,
  ExternalLink,
  Settings,
  Eye,
  Users
} from 'lucide-react';

interface AnalyticsConfigProps {
  settings: Record<string, any>;
  loading: { settings: boolean; saving: boolean };
  errors: Record<string, string | undefined>;
  onUpdateSetting: (key: string, value: any) => void;
}

export function AnalyticsConfig({ 
  settings, 
  loading, 
  errors, 
  onUpdateSetting 
}: AnalyticsConfigProps) {
  const handleAnalyticsChange = (key: string, value: any) => {
    const currentAnalytics = settings.analytics_tracking || {};
    onUpdateSetting('analytics_tracking', {
      ...currentAnalytics,
      [key]: value,
    });
  };

  const handleReportingChange = (key: string, value: any) => {
    const currentReporting = settings.custom_reporting || {};
    onUpdateSetting('custom_reporting', {
      ...currentReporting,
      [key]: value,
    });
  };

  const handlePrivacyChange = (key: string, value: any) => {
    const currentPrivacy = settings.data_privacy || {};
    onUpdateSetting('data_privacy', {
      ...currentPrivacy,
      [key]: value,
    });
  };

  const handleExportChange = (key: string, value: any) => {
    const currentExport = settings.export_settings || {};
    onUpdateSetting('export_settings', {
      ...currentExport,
      [key]: value,
    });
  };

  const handlePerformanceChange = (key: string, value: any) => {
    const currentPerformance = settings.performance_monitoring || {};
    onUpdateSetting('performance_monitoring', {
      ...currentPerformance,
      [key]: value,
    });
  };

  return (
    <div className="space-y-6">
      {/* Analytics Tracking Setup */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Analytics Tracking Setup
          </CardTitle>
          <CardDescription>
            Configure analytics tracking and data collection
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Analytics Tracking</Label>
              <p className="text-sm text-muted-foreground">
                Track user interactions and content performance
              </p>
            </div>
            <Switch
              checked={settings.analytics_tracking?.enabled || false}
              onCheckedChange={(checked) => handleAnalyticsChange('enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.analytics_tracking?.enabled && (
            <>
              <div className="space-y-2">
                <Label htmlFor="google-analytics">Google Analytics Tracking ID</Label>
                <Input
                  id="google-analytics"
                  value={settings.analytics_tracking?.google_analytics_id || ''}
                  onChange={(e) => handleAnalyticsChange('google_analytics_id', e.target.value)}
                  placeholder="G-XXXXXXXXXX or UA-XXXXXXXXX-X"
                  disabled={loading.saving}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="gtm-id">Google Tag Manager ID</Label>
                <Input
                  id="gtm-id"
                  value={settings.analytics_tracking?.gtm_id || ''}
                  onChange={(e) => handleAnalyticsChange('gtm_id', e.target.value)}
                  placeholder="GTM-XXXXXXX"
                  disabled={loading.saving}
                />
              </div>

              <div className="space-y-3">
                <Label>Tracking Options</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="track-page-views"
                      checked={settings.analytics_tracking?.track_page_views !== false}
                      onCheckedChange={(checked) => handleAnalyticsChange('track_page_views', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="track-page-views" className="text-sm">Track page views</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="track-user-interactions"
                      checked={settings.analytics_tracking?.track_user_interactions || false}
                      onCheckedChange={(checked) => handleAnalyticsChange('track_user_interactions', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="track-user-interactions" className="text-sm">Track user interactions (clicks, scrolls)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="track-downloads"
                      checked={settings.analytics_tracking?.track_downloads || false}
                      onCheckedChange={(checked) => handleAnalyticsChange('track_downloads', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="track-downloads" className="text-sm">Track file downloads</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="track-outbound-links"
                      checked={settings.analytics_tracking?.track_outbound_links || false}
                      onCheckedChange={(checked) => handleAnalyticsChange('track_outbound_links', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="track-outbound-links" className="text-sm">Track outbound link clicks</Label>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Custom Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Custom Report Generation
          </CardTitle>
          <CardDescription>
            Configure automated report generation and scheduling
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Custom Reports</Label>
              <p className="text-sm text-muted-foreground">
                Generate and schedule custom analytics reports
              </p>
            </div>
            <Switch
              checked={settings.custom_reporting?.enabled || false}
              onCheckedChange={(checked) => handleReportingChange('enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.custom_reporting?.enabled && (
            <>
              <div className="space-y-2">
                <Label htmlFor="report-frequency">Report Generation Frequency</Label>
                <Select
                  value={settings.custom_reporting?.frequency || 'weekly'}
                  onValueChange={(value) => handleReportingChange('frequency', value)}
                  disabled={loading.saving}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select frequency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="daily">Daily</SelectItem>
                    <SelectItem value="weekly">Weekly</SelectItem>
                    <SelectItem value="monthly">Monthly</SelectItem>
                    <SelectItem value="quarterly">Quarterly</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="report-recipients">Report Recipients (emails)</Label>
                <Textarea
                  id="report-recipients"
                  value={settings.custom_reporting?.recipients || ''}
                  onChange={(e) => handleReportingChange('recipients', e.target.value)}
                  placeholder="<EMAIL>, <EMAIL>"
                  rows={2}
                  disabled={loading.saving}
                />
                <p className="text-xs text-muted-foreground">
                  Separate multiple email addresses with commas
                </p>
              </div>

              <div className="space-y-3">
                <Label>Report Content</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-traffic"
                      checked={settings.custom_reporting?.include_traffic !== false}
                      onCheckedChange={(checked) => handleReportingChange('include_traffic', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="include-traffic" className="text-sm">Website traffic statistics</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-content"
                      checked={settings.custom_reporting?.include_content_performance !== false}
                      onCheckedChange={(checked) => handleReportingChange('include_content_performance', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="include-content" className="text-sm">Content performance metrics</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-user-activity"
                      checked={settings.custom_reporting?.include_user_activity || false}
                      onCheckedChange={(checked) => handleReportingChange('include_user_activity', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="include-user-activity" className="text-sm">User activity summary</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-system-health"
                      checked={settings.custom_reporting?.include_system_health || false}
                      onCheckedChange={(checked) => handleReportingChange('include_system_health', checked)}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="include-system-health" className="text-sm">System health metrics</Label>
                  </div>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Data Retention & Privacy */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Data Retention & Privacy Settings
          </CardTitle>
          <CardDescription>
            Configure data retention policies and privacy compliance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="data-retention">Analytics Data Retention (days)</Label>
            <Input
              id="data-retention"
              type="number"
              value={settings.data_privacy?.retention_days || 365}
              onChange={(e) => handlePrivacyChange('retention_days', parseInt(e.target.value))}
              disabled={loading.saving}
              min="30"
              max="2555" // 7 years
            />
            <p className="text-xs text-muted-foreground">
              How long to keep analytics data before automatic deletion
            </p>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Anonymize IP Addresses</Label>
              <p className="text-sm text-muted-foreground">
                Remove last octet of IP addresses for privacy compliance
              </p>
            </div>
            <Switch
              checked={settings.data_privacy?.anonymize_ips !== false}
              onCheckedChange={(checked) => handlePrivacyChange('anonymize_ips', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>GDPR Compliance Mode</Label>
              <p className="text-sm text-muted-foreground">
                Enable additional privacy protections for EU users
              </p>
            </div>
            <Switch
              checked={settings.data_privacy?.gdpr_compliance || false}
              onCheckedChange={(checked) => handlePrivacyChange('gdpr_compliance', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Cookie Consent Required</Label>
              <p className="text-sm text-muted-foreground">
                Require user consent before setting tracking cookies
              </p>
            </div>
            <Switch
              checked={settings.data_privacy?.require_cookie_consent || false}
              onCheckedChange={(checked) => handlePrivacyChange('require_cookie_consent', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>

      {/* Export & Integration Configuration */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export & Integration Configuration
          </CardTitle>
          <CardDescription>
            Configure data export options and third-party integrations
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Data Export</Label>
              <p className="text-sm text-muted-foreground">
                Allow exporting analytics data in various formats
              </p>
            </div>
            <Switch
              checked={settings.export_settings?.enabled !== false}
              onCheckedChange={(checked) => handleExportChange('enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.export_settings?.enabled !== false && (
            <>
              <div className="space-y-3">
                <Label>Available Export Formats</Label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="export-csv"
                      checked={settings.export_settings?.formats?.includes('csv') !== false}
                      onCheckedChange={(checked) => {
                        const formats = settings.export_settings?.formats || ['csv', 'json'];
                        if (checked) {
                          handleExportChange('formats', [...formats.filter((f: string) => f !== 'csv'), 'csv']);
                        } else {
                          handleExportChange('formats', formats.filter((f: string) => f !== 'csv'));
                        }
                      }}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="export-csv" className="text-sm">CSV (Comma Separated Values)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="export-json"
                      checked={settings.export_settings?.formats?.includes('json') !== false}
                      onCheckedChange={(checked) => {
                        const formats = settings.export_settings?.formats || ['csv', 'json'];
                        if (checked) {
                          handleExportChange('formats', [...formats.filter((f: string) => f !== 'json'), 'json']);
                        } else {
                          handleExportChange('formats', formats.filter((f: string) => f !== 'json'));
                        }
                      }}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="export-json" className="text-sm">JSON (JavaScript Object Notation)</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="export-pdf"
                      checked={settings.export_settings?.formats?.includes('pdf') || false}
                      onCheckedChange={(checked) => {
                        const formats = settings.export_settings?.formats || ['csv', 'json'];
                        if (checked) {
                          handleExportChange('formats', [...formats.filter((f: string) => f !== 'pdf'), 'pdf']);
                        } else {
                          handleExportChange('formats', formats.filter((f: string) => f !== 'pdf'));
                        }
                      }}
                      disabled={loading.saving}
                    />
                    <Label htmlFor="export-pdf" className="text-sm">PDF (Portable Document Format)</Label>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="export-limit">Maximum Export Records</Label>
                <Input
                  id="export-limit"
                  type="number"
                  value={settings.export_settings?.max_records || 10000}
                  onChange={(e) => handleExportChange('max_records', parseInt(e.target.value))}
                  disabled={loading.saving}
                  min="100"
                  max="100000"
                />
              </div>
            </>
          )}

          <div className="space-y-2">
            <Label htmlFor="webhook-url">Analytics Webhook URL</Label>
            <Input
              id="webhook-url"
              type="url"
              value={settings.export_settings?.webhook_url || ''}
              onChange={(e) => handleExportChange('webhook_url', e.target.value)}
              placeholder="https://your-service.com/analytics-webhook"
              disabled={loading.saving}
            />
            <p className="text-xs text-muted-foreground">
              Send real-time analytics data to external services
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Performance Monitoring Thresholds */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Performance Monitoring Thresholds
          </CardTitle>
          <CardDescription>
            Set thresholds for performance alerts and monitoring
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Performance Alerts</Label>
              <p className="text-sm text-muted-foreground">
                Send alerts when performance thresholds are exceeded
              </p>
            </div>
            <Switch
              checked={settings.performance_monitoring?.alerts_enabled || false}
              onCheckedChange={(checked) => handlePerformanceChange('alerts_enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.performance_monitoring?.alerts_enabled && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="page-load-threshold">Page Load Time Threshold (ms)</Label>
                  <Input
                    id="page-load-threshold"
                    type="number"
                    value={settings.performance_monitoring?.page_load_threshold || 3000}
                    onChange={(e) => handlePerformanceChange('page_load_threshold', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="500"
                    max="10000"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bounce-rate-threshold">Bounce Rate Threshold (%)</Label>
                  <Input
                    id="bounce-rate-threshold"
                    type="number"
                    value={settings.performance_monitoring?.bounce_rate_threshold || 70}
                    onChange={(e) => handlePerformanceChange('bounce_rate_threshold', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="10"
                    max="100"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="error-rate-threshold">Error Rate Threshold (%)</Label>
                  <Input
                    id="error-rate-threshold"
                    type="number"
                    value={settings.performance_monitoring?.error_rate_threshold || 5}
                    onChange={(e) => handlePerformanceChange('error_rate_threshold', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="1"
                    max="50"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="traffic-drop-threshold">Traffic Drop Alert (%)</Label>
                  <Input
                    id="traffic-drop-threshold"
                    type="number"
                    value={settings.performance_monitoring?.traffic_drop_threshold || 25}
                    onChange={(e) => handlePerformanceChange('traffic_drop_threshold', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="10"
                    max="90"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="alert-recipients">Alert Recipients</Label>
                <Textarea
                  id="alert-recipients"
                  value={settings.performance_monitoring?.alert_recipients || ''}
                  onChange={(e) => handlePerformanceChange('alert_recipients', e.target.value)}
                  placeholder="<EMAIL>, <EMAIL>"
                  rows={2}
                  disabled={loading.saving}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
