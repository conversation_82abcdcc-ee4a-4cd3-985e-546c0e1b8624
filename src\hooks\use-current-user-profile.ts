'use client';

import { createClient } from '@/lib/client';
import { useEffect, useState } from 'react';
import type { Profile } from '@/types';

interface UseCurrentUserProfileReturn {
  profile: Profile | null;
  loading: boolean;
  error: string | null;
  isAdmin: boolean;
  isPublisher: boolean;
  isEditor: boolean;
  isMember: boolean;
  hasRole: (role: string) => boolean;
  refresh: () => Promise<void>;
}

export const useCurrentUserProfile = (): UseCurrentUserProfileReturn => {
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProfile = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const supabase = createClient();
      
      // First get the current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        throw new Error(`Authentication error: ${authError.message}`);
      }
      
      if (!user) {
        setProfile(null);
        return;
      }

      // Then fetch their profile from the database
      const { data: profileData, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .is('deleted_at', null)
        .single();

      if (profileError) {
        throw new Error(`Profile fetch error: ${profileError.message}`);
      }

      setProfile(profileData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user profile';
      setError(errorMessage);
      console.error('Error fetching user profile:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProfile();
  }, []);

  // Helper functions for role checking
  const hasRole = (requiredRole: string): boolean => {
    if (!profile) return false;
    
    switch (requiredRole.toLowerCase()) {
      case 'admin':
        return profile.role === 'admin';
      case 'publisher':
        return ['admin', 'publisher'].includes(profile.role);
      case 'editor':
        return ['admin', 'publisher', 'editor'].includes(profile.role);
      case 'member':
        return ['admin', 'publisher', 'editor', 'member'].includes(profile.role);
      default:
        return false;
    }
  };

  return {
    profile,
    loading,
    error,
    isAdmin: hasRole('admin'),
    isPublisher: hasRole('publisher'),
    isEditor: hasRole('editor'),
    isMember: hasRole('member'),
    hasRole,
    refresh: fetchProfile,
  };
};
