'use client';

import { useState } from 'react';
// Card components imported but not used in main page - used in child components
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { But<PERSON> } from '@/components/ui/button';
import {
  Building2,
  FileText,
  Users,
  Activity,
  BarChart3,
  AlertTriangle,
  RefreshCw,
  Save,
} from 'lucide-react';
import { useSettingsManagement } from '@/hooks/use-settings-management';
import { OrganizationConfig } from '@/components/settings/OrganizationConfig';
import { ContentPolicies } from '@/components/settings/ContentPolicies';
import { UserAccessManagement } from '@/components/settings/UserAccessManagement';
import { SystemMonitoring } from '@/components/settings/SystemMonitoring';
import { AnalyticsConfig } from '@/components/settings/AnalyticsConfig';

export default function SettingsPage() {
  const {
    settings,
    loading,
    errors,
    hasUnsavedChanges,
    updateSetting,
    saveSettings,
    refresh,
  } = useSettingsManagement();

  const [activeTab, setActiveTab] = useState('organization');

  const handleSaveAll = async () => {
    const result = await saveSettings();
    if (result.success) {
      // Toast notification would go here
      console.log('Settings saved successfully');
    }
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage organization-wide settings and system configuration
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={loading.settings}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${
                loading.settings ? 'animate-spin' : ''
              }`}
            />
            Refresh
          </Button>
          {hasUnsavedChanges && (
            <Button size="sm" onClick={handleSaveAll} disabled={loading.saving}>
              <Save className="h-4 w-4 mr-2" />
              Save Changes
            </Button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {errors.general && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{errors.general}</AlertDescription>
        </Alert>
      )}

      {/* Unsaved Changes Warning */}
      {hasUnsavedChanges && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            You have unsaved changes. Don&apos;t forget to save your settings.
          </AlertDescription>
        </Alert>
      )}

      {/* Main Settings Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="organization">
            <Building2 className="h-4 w-4 mr-2" />
            Organization
          </TabsTrigger>
          <TabsTrigger value="content">
            <FileText className="h-4 w-4 mr-2" />
            Content
          </TabsTrigger>
          <TabsTrigger value="users">
            <Users className="h-4 w-4 mr-2" />
            Users & Access
          </TabsTrigger>
          <TabsTrigger value="system">
            <Activity className="h-4 w-4 mr-2" />
            System
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="organization" className="space-y-4">
          <OrganizationConfig
            settings={settings}
            loading={loading}
            errors={errors}
            onUpdateSetting={updateSetting}
          />
        </TabsContent>

        <TabsContent value="content" className="space-y-4">
          <ContentPolicies
            settings={settings}
            loading={loading}
            errors={errors}
            onUpdateSetting={updateSetting}
          />
        </TabsContent>

        <TabsContent value="users" className="space-y-4">
          <UserAccessManagement
            settings={settings}
            loading={loading}
            errors={errors}
            onUpdateSetting={updateSetting}
          />
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <SystemMonitoring
            settings={settings}
            loading={loading}
            errors={errors}
            onUpdateSetting={updateSetting}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <AnalyticsConfig
            settings={settings}
            loading={loading}
            errors={errors}
            onUpdateSetting={updateSetting}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
