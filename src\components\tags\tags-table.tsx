'use client';

import { useState } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Copy,
  Merge,
  Hash,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import type { Tag } from '@/types';

interface TagWithStats extends Tag {
  post_count?: number;
  usage_trend?: 'up' | 'down' | 'stable';
}

interface TagsTableProps {
  tags: TagWithStats[];
  isLoading?: boolean;
  selectedTags?: TagWithStats[];
  onSelectionChange?: (tags: TagWithStats[]) => void;
  onEdit?: (tag: TagWithStats) => void;
  onDelete?: (tag: TagWithStats) => void;
  onView?: (tag: TagWithStats) => void;
  onDuplicate?: (tag: TagWithStats) => void;
  onMerge?: (tags: TagWithStats[]) => void;
}

export function TagsTable({
  tags,
  isLoading = false,
  selectedTags = [],
  onSelectionChange,
  onEdit,
  onDelete,
  onView,
  onDuplicate,
  onMerge,
}: TagsTableProps) {
  const [sortField, setSortField] = useState<keyof TagWithStats>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? tags : []);
    }
  };

  const handleSelectTag = (tag: TagWithStats, checked: boolean) => {
    if (onSelectionChange) {
      if (checked) {
        onSelectionChange([...selectedTags, tag]);
      } else {
        onSelectionChange(selectedTags.filter(t => t.id !== tag.id));
      }
    }
  };

  const isSelected = (tag: TagWithStats) => 
    selectedTags.some(t => t.id === tag.id);

  const allSelected = tags.length > 0 && 
    tags.every(tag => isSelected(tag));

  const someSelected = selectedTags.length > 0 && !allSelected;

  const getTrendIcon = (trend?: string) => {
    switch (trend) {
      case 'up':
        return '📈';
      case 'down':
        return '📉';
      default:
        return '➡️';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    );
  }

  if (tags.length === 0) {
    return (
      <div className="text-center py-12">
        <Hash className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No tags found</h3>
        <p className="text-muted-foreground mb-4">
          Get started by creating your first tag.
        </p>
      </div>
    );
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12">
              <Checkbox
                checked={allSelected}
                onCheckedChange={handleSelectAll}
                aria-label="Select all tags"
                {...(someSelected && { 'data-state': 'indeterminate' })}
              />
            </TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Slug</TableHead>
            <TableHead>Usage</TableHead>
            <TableHead>Trend</TableHead>
            <TableHead>Created</TableHead>
            <TableHead>Updated</TableHead>
            <TableHead className="w-12"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tags.map((tag) => (
            <TableRow key={tag.id}>
              <TableCell>
                <Checkbox
                  checked={isSelected(tag)}
                  onCheckedChange={(checked) => 
                    handleSelectTag(tag, checked as boolean)
                  }
                  aria-label={`Select ${tag.name}`}
                />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  <Hash className="h-4 w-4 text-muted-foreground" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{tag.name}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell>
                <code className="text-sm bg-muted px-2 py-1 rounded">
                  {tag.slug}
                </code>
              </TableCell>
              <TableCell>
                <Badge variant="secondary">
                  {tag.post_count || 0} posts
                </Badge>
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1">
                  <span className="text-sm">
                    {getTrendIcon(tag.usage_trend)}
                  </span>
                  <span className="text-xs text-muted-foreground">
                    {tag.usage_trend || 'stable'}
                  </span>
                </div>
              </TableCell>
              <TableCell className="text-sm text-muted-foreground">
                {formatDistanceToNow(new Date(tag.created_at || ''), { 
                  addSuffix: true 
                })}
              </TableCell>
              <TableCell className="text-sm text-muted-foreground">
                {formatDistanceToNow(new Date(tag.updated_at || ''), { 
                  addSuffix: true 
                })}
              </TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => onView?.(tag)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Posts
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onEdit?.(tag)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onDuplicate?.(tag)}>
                      <Copy className="mr-2 h-4 w-4" />
                      Duplicate
                    </DropdownMenuItem>
                    {selectedTags.length > 1 && (
                      <DropdownMenuItem onClick={() => onMerge?.(selectedTags)}>
                        <Merge className="mr-2 h-4 w-4" />
                        Merge Selected
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem 
                      onClick={() => onDelete?.(tag)}
                      className="text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
