'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  CategoriesTable,
  CategoriesFilters,
  CategoryDialog,
  CategoryHierarchy,
  CategoryAnalytics,
  BulkCategoryActions,
} from '@/components/categories';
import { useCategoriesManagement } from '@/hooks/use-categories-management';
import {
  Plus,
  RefreshCw,
  AlertTriangle,
  Folder,
  BarChart3,
  TreePine,
  Table,
} from 'lucide-react';
import type { Category } from '@/types';

export default function CategoriesPage() {
  const {
    categories,
    filteredCategories,
    loading,
    errors,
    filters,
    updateFilters,
    resetFilters,
    refresh,
    createNewCategory,
    updateExistingCategory,
    deleteCategories,
    duplicateCategories,
    exportCategories,
  } = useCategoriesManagement();

  const [selectedCategories, setSelectedCategories] = useState<Category[]>([]);
  const [showCategoryDialog, setShowCategoryDialog] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [activeTab, setActiveTab] = useState('table');

  // Handle category actions
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setShowCategoryDialog(true);
  };

  const handleDelete = async (category: Category) => {
    try {
      await deleteCategories([category]);
    } catch (error) {
      console.error('Failed to delete category:', error);
    }
  };

  const handleDuplicate = async (category: Category) => {
    try {
      await duplicateCategories([category]);
    } catch (error) {
      console.error('Failed to duplicate category:', error);
    }
  };

  const handleViewPosts = (category: Category) => {
    // Navigate to posts page with category filter
    window.location.href = `/posts?category=${category.id}`;
  };

  const handleAddChild = (parentCategory: Category) => {
    // Pre-fill parent category when creating new category
    setEditingCategory({
      ...parentCategory,
      id: '',
      name: '',
      slug: '',
      description: '',
      parent_id: parentCategory.id,
    } as Category);
    setShowCategoryDialog(true);
  };

  const handleSaveCategory = async (categoryData: Partial<Category>) => {
    if (editingCategory?.id) {
      // Update existing category
      return await updateExistingCategory(editingCategory.id, categoryData);
    } else {
      // Create new category
      return await createNewCategory(categoryData);
    }
  };

  const handleDialogClose = () => {
    setShowCategoryDialog(false);
    setEditingCategory(null);
  };

  const handleBulkDelete = async (categoriesToDelete: Category[]) => {
    try {
      await deleteCategories(categoriesToDelete);
      setSelectedCategories([]);
    } catch (error) {
      console.error('Failed to delete categories:', error);
    }
  };

  const handleBulkDuplicate = async (categoriesToDuplicate: Category[]) => {
    try {
      await duplicateCategories(categoriesToDuplicate);
      setSelectedCategories([]);
    } catch (error) {
      console.error('Failed to duplicate categories:', error);
    }
  };

  const handleBulkExport = async (categoriesToExport: Category[]) => {
    try {
      await exportCategories(categoriesToExport);
    } catch (error) {
      console.error('Failed to export categories:', error);
    }
  };

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      {/* Header */}
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Categories</h2>
          <p className="text-muted-foreground">
            Organize your content with categories and subcategories
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={loading.categories}
          >
            <RefreshCw
              className={`mr-2 h-4 w-4 ${
                loading.categories ? 'animate-spin' : ''
              }`}
            />
            Refresh
          </Button>
          <Button onClick={() => setShowCategoryDialog(true)}>
            <Plus className="mr-2 h-4 w-4" />
            New Category
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {(errors.categories || errors.actions) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {errors.categories || errors.actions}
          </AlertDescription>
        </Alert>
      )}

      {/* Stats Overview */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Categories
            </CardTitle>
            <Folder className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
            <p className="text-xs text-muted-foreground">
              {categories.filter((c) => !c.parent_id).length} top-level
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Posts</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {categories.reduce((sum, cat) => sum + (cat.post_count || 0), 0)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all categories
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Filtered Results
            </CardTitle>
            <Table className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {filteredCategories.length}
            </div>
            <p className="text-xs text-muted-foreground">
              Matching current filters
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Selected</CardTitle>
            <TreePine className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {selectedCategories.length}
            </div>
            <p className="text-xs text-muted-foreground">Categories selected</p>
          </CardContent>
        </Card>
      </div>

      {/* Bulk Actions */}
      <BulkCategoryActions
        selectedCategories={selectedCategories}
        onDelete={handleBulkDelete}
        onDuplicate={handleBulkDuplicate}
        onExport={handleBulkExport}
        disabled={loading.actions}
      />

      {/* Main Content Tabs */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="table" className="flex items-center gap-2">
            <Table className="h-4 w-4" />
            Table View
            <Badge variant="secondary">{filteredCategories.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="hierarchy" className="flex items-center gap-2">
            <TreePine className="h-4 w-4" />
            Hierarchy
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Table View */}
        <TabsContent value="table" className="space-y-4">
          <CategoriesFilters
            filters={filters}
            onFiltersChange={updateFilters}
            onReset={resetFilters}
            categories={categories}
            loading={loading.categories}
          />

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Folder className="h-5 w-5" />
                Categories
                <Badge variant="secondary">{filteredCategories.length}</Badge>
              </CardTitle>
              <CardDescription>
                Manage your content categories with advanced filtering and bulk
                operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CategoriesTable
                categories={filteredCategories}
                isLoading={loading.categories}
                selectedCategories={selectedCategories}
                onSelectionChange={setSelectedCategories}
                onEdit={handleEdit}
                onDelete={handleDelete}
                onView={handleViewPosts}
                onDuplicate={handleDuplicate}
              />
            </CardContent>
          </Card>
        </TabsContent>

        {/* Hierarchy View */}
        <TabsContent value="hierarchy" className="space-y-4">
          <CategoryHierarchy
            categories={categories}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onAddChild={handleAddChild}
            onViewPosts={handleViewPosts}
          />
        </TabsContent>

        {/* Analytics View */}
        <TabsContent value="analytics" className="space-y-4">
          <CategoryAnalytics categories={categories} />
        </TabsContent>
      </Tabs>

      {/* Category Dialog */}
      <CategoryDialog
        isOpen={showCategoryDialog}
        onOpenChange={handleDialogClose}
        category={editingCategory}
        categories={categories}
        onSave={handleSaveCategory}
        loading={loading.actions}
      />
    </div>
  );
}
