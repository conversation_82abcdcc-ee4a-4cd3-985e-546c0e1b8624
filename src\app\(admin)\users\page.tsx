'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  UsersTable,
  UsersFilters,
  UserInvitationDialog,
  BulkUserActions,
  UserActivityTimeline,
  UserRoleManagement,
} from '@/components/users';
import { useUsersManagement } from '@/hooks/use-users-management';
import {
  Users,
  RefreshCw,
  Mail,
  AlertTriangle,
  UserPlus,
  Activity,
  Settings,
  Shield,
} from 'lucide-react';
import type { Profile, UserRole } from '@/types';

export default function UsersPage() {
  const {
    users,
    invitations,
    userActivity,
    pagination,
    loading,
    errors,
    filters,
    invitationFilters,
    updateFilters,
    updateInvitationFilters,
    resetFilters,
    refresh,
    refreshInvitations,
    refreshActivity,
    updateUser,
    deleteUser,
    bulkRoleChange,
    bulkDelete,
  } = useUsersManagement();

  const [selectedUsers, setSelectedUsers] = useState<Profile[]>([]);
  const [showInvitationDialog, setShowInvitationDialog] = useState(false);
  const [selectedUserForRole, setSelectedUserForRole] =
    useState<Profile | null>(null);
  const [activeTab, setActiveTab] = useState('users');

  // Handle individual user actions
  const handleEditUser = (user: Profile) => {
    // Navigate to user edit page
    window.location.href = `/users/${user.id}/edit`;
  };

  const handleDeleteUser = async (user: Profile) => {
    const result = await deleteUser(user.id);
    if (result.success) {
      // Remove from selected users if it was selected
      setSelectedUsers((prev) => prev.filter((u) => u.id !== user.id));
    }
    return result;
  };

  const handleRoleChange = async (user: Profile, role: UserRole) => {
    const result = await updateUser(user.id, { role });
    if (result.success) {
      // Update selected users if this user was selected
      setSelectedUsers((prev) =>
        prev.map((u) => (u.id === user.id ? { ...u, role } : u))
      );
    }
    return result;
  };

  // Handle bulk actions
  const handleBulkRoleChange = async (userIds: string[], role: UserRole) => {
    const result = await bulkRoleChange(userIds, role);
    if (result.success) {
      setSelectedUsers([]);
    }
    return result;
  };

  const handleBulkDelete = async (userIds: string[]) => {
    const result = await bulkDelete(userIds);
    if (result.success) {
      setSelectedUsers([]);
    }
    return result;
  };

  const handleInvitationSuccess = () => {
    refreshInvitations();
    refresh();
  };

  const handleRoleManagementSuccess = async (
    userId: string,
    newRole: UserRole
  ) => {
    const result = await updateUser(userId, { role: newRole });
    if (result.success) {
      setSelectedUserForRole(null);
      refresh();
    }
    return result;
  };

  // Calculate stats
  const totalUsers = pagination.users?.total || 0;
  const pendingInvitations = invitations.filter(
    (inv) => inv.status === 'pending'
  ).length;
  const adminUsers = users.filter((user) => user.role === 'admin').length;
  const activeUsers = users.filter((user) => !user.deleted_at).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Users Management
          </h1>
          <p className="text-muted-foreground">
            Manage user accounts, roles, and permissions
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={refresh} disabled={loading.users}>
            <RefreshCw
              className={`mr-2 h-4 w-4 ${loading.users ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Button onClick={() => setShowInvitationDialog(true)}>
            <UserPlus className="mr-2 h-4 w-4" />
            Invite Users
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers}</div>
            <p className="text-xs text-muted-foreground">
              {activeUsers} active users
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Pending Invitations
            </CardTitle>
            <Mail className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{pendingInvitations}</div>
            <p className="text-xs text-muted-foreground">Awaiting acceptance</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Administrators
            </CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{adminUsers}</div>
            <p className="text-xs text-muted-foreground">Full access users</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Recent Activity
            </CardTitle>
            <Activity className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{userActivity.length}</div>
            <p className="text-xs text-muted-foreground">Actions logged</p>
          </CardContent>
        </Card>
      </div>

      {/* Error Display */}
      {(errors.users || errors.invitations || errors.actions) && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {errors.users || errors.invitations || errors.actions}
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList>
          <TabsTrigger value="users" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Users
            <Badge variant="secondary">{totalUsers}</Badge>
          </TabsTrigger>
          <TabsTrigger value="invitations" className="flex items-center gap-2">
            <Mail className="h-4 w-4" />
            Invitations
            <Badge variant="secondary">{invitations.length}</Badge>
          </TabsTrigger>
          <TabsTrigger value="activity" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Activity
          </TabsTrigger>
        </TabsList>

        {/* Users Tab */}
        <TabsContent value="users" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <CardTitle>User Accounts</CardTitle>
                  <CardDescription>
                    Manage user accounts, roles, and permissions
                  </CardDescription>
                </div>
                <BulkUserActions
                  selectedUsers={selectedUsers}
                  onRoleChange={handleBulkRoleChange}
                  onDelete={handleBulkDelete}
                  onInvite={() => setShowInvitationDialog(true)}
                  disabled={loading.actions}
                />
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <UsersFilters
                filters={filters}
                onFiltersChange={updateFilters}
                onReset={resetFilters}
                loading={loading.users}
              />

              <UsersTable
                users={users}
                selectedUsers={selectedUsers}
                onSelectionChange={setSelectedUsers}
                onEdit={handleEditUser}
                onDelete={handleDeleteUser}
                onRoleChange={handleRoleChange}
                loading={loading.users}
              />

              {pagination.users && pagination.users.totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <p className="text-sm text-muted-foreground">
                    Showing{' '}
                    {(pagination.users.page - 1) * pagination.users.limit + 1}{' '}
                    to{' '}
                    {Math.min(
                      pagination.users.page * pagination.users.limit,
                      pagination.users.total
                    )}{' '}
                    of {pagination.users.total} users
                  </p>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        updateFilters({ page: pagination.users!.page - 1 })
                      }
                      disabled={!pagination.users.hasPrev || loading.users}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        updateFilters({ page: pagination.users!.page + 1 })
                      }
                      disabled={!pagination.users.hasNext || loading.users}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Invitations Tab */}
        <TabsContent value="invitations" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                  <CardTitle>User Invitations</CardTitle>
                  <CardDescription>
                    Manage pending and sent invitations
                  </CardDescription>
                </div>
                <Button onClick={() => setShowInvitationDialog(true)}>
                  <Mail className="mr-2 h-4 w-4" />
                  Send Invitation
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {loading.invitations ? (
                <div className="text-center py-8">
                  <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    Loading invitations...
                  </p>
                </div>
              ) : invitations.length === 0 ? (
                <div className="text-center py-8">
                  <Mail className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <h3 className="text-lg font-semibold mb-2">
                    No invitations found
                  </h3>
                  <p className="text-muted-foreground mb-4">
                    Start by sending invitations to new users
                  </p>
                  <Button onClick={() => setShowInvitationDialog(true)}>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Send First Invitation
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {invitations.map((invitation) => (
                    <div
                      key={invitation.id}
                      className="flex items-center justify-between p-4 border rounded-lg"
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium">
                            {invitation.email}
                          </span>
                          <Badge
                            variant={
                              invitation.status === 'pending'
                                ? 'default'
                                : invitation.status === 'accepted'
                                ? 'secondary'
                                : 'destructive'
                            }
                          >
                            {invitation.status}
                          </Badge>
                          <Badge variant="outline">
                            {invitation.role_to_assign}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">
                          Invited{' '}
                          {new Date(
                            invitation.created_at || ''
                          ).toLocaleDateString()}
                          {invitation.invited_by && (
                            <span>
                              {' '}
                              by{' '}
                              {invitation.invited_by.full_name ||
                                invitation.invited_by.email}
                            </span>
                          )}
                        </p>
                      </div>
                      <div className="flex items-center gap-2">
                        {invitation.status === 'pending' && (
                          <>
                            <Button variant="outline" size="sm">
                              Resend
                            </Button>
                            <Button variant="outline" size="sm">
                              Cancel
                            </Button>
                          </>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Activity Tab */}
        <TabsContent value="activity" className="space-y-4">
          <div className="grid gap-6 lg:grid-cols-3">
            <div className="lg:col-span-2">
              <UserActivityTimeline />
            </div>
            <div className="space-y-4">
              {selectedUserForRole && (
                <UserRoleManagement
                  user={selectedUserForRole}
                  onRoleChange={handleRoleManagementSuccess}
                  onClose={() => setSelectedUserForRole(null)}
                />
              )}
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Dialogs */}
      <UserInvitationDialog
        open={showInvitationDialog}
        onOpenChange={setShowInvitationDialog}
        onSuccess={handleInvitationSuccess}
      />
    </div>
  );
}
