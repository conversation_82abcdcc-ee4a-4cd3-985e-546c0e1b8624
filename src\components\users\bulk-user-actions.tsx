'use client';

import { useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  MoreHorizontal, 
  Shield, 
  Edit, 
  User, 
  Trash2, 
  Mail,
  Users
} from 'lucide-react';
import type { Profile, UserRole } from '@/types';

interface BulkUserActionsProps {
  selectedUsers: Profile[];
  onRoleChange: (userIds: string[], role: UserRole) => Promise<{ success: boolean; error?: string }>;
  onDelete: (userIds: string[]) => Promise<{ success: boolean; error?: string }>;
  onInvite: () => void;
  disabled?: boolean;
}

const roleOptions: { value: UserRole; label: string; icon: React.ReactNode }[] = [
  { value: 'admin', label: 'Admin', icon: <Shield className="h-4 w-4" /> },
  { value: 'publisher', label: 'Publisher', icon: <Edit className="h-4 w-4" /> },
  { value: 'editor', label: 'Editor', icon: <Edit className="h-4 w-4" /> },
  { value: 'member', label: 'Member', icon: <User className="h-4 w-4" /> },
];

export function BulkUserActions({
  selectedUsers,
  onRoleChange,
  onDelete,
  onInvite,
  disabled = false,
}: BulkUserActionsProps) {
  const [showRoleDialog, setShowRoleDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedRole, setSelectedRole] = useState<UserRole>('member');
  const [loading, setLoading] = useState(false);

  const selectedCount = selectedUsers.length;
  const selectedUserIds = selectedUsers.map(user => user.id);

  const handleRoleChange = async () => {
    setLoading(true);
    try {
      const result = await onRoleChange(selectedUserIds, selectedRole);
      if (result.success) {
        setShowRoleDialog(false);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    setLoading(true);
    try {
      const result = await onDelete(selectedUserIds);
      if (result.success) {
        setShowDeleteDialog(false);
      }
    } finally {
      setLoading(false);
    }
  };

  if (selectedCount === 0) {
    return (
      <div className="flex items-center gap-2">
        <Button onClick={onInvite} disabled={disabled}>
          <Mail className="mr-2 h-4 w-4" />
          Invite Users
        </Button>
      </div>
    );
  }

  return (
    <>
      <div className="flex items-center gap-2">
        <Badge variant="secondary" className="gap-1">
          <Users className="h-3 w-3" />
          {selectedCount} selected
        </Badge>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" disabled={disabled}>
              <MoreHorizontal className="h-4 w-4 mr-2" />
              Bulk Actions
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>User Actions</DropdownMenuLabel>
            <DropdownMenuItem onClick={() => setShowRoleDialog(true)}>
              <Shield className="mr-2 h-4 w-4" />
              Change Role
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              onClick={() => setShowDeleteDialog(true)}
              className="text-destructive"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Delete Users
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button onClick={onInvite} disabled={disabled}>
          <Mail className="mr-2 h-4 w-4" />
          Invite Users
        </Button>
      </div>

      {/* Role Change Dialog */}
      <AlertDialog open={showRoleDialog} onOpenChange={setShowRoleDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Change User Roles</AlertDialogTitle>
            <AlertDialogDescription>
              You are about to change the role for {selectedCount} user{selectedCount > 1 ? 's' : ''}:
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Selected Users:</h4>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {selectedUsers.map((user) => (
                  <div key={user.id} className="flex items-center justify-between text-sm p-2 bg-muted rounded">
                    <span>{user.full_name || user.email}</span>
                    <Badge variant="outline" className="text-xs">
                      {user.role}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>

            <div className="space-y-2">
              <h4 className="text-sm font-medium">New Role:</h4>
              <Select value={selectedRole} onValueChange={(value: UserRole) => setSelectedRole(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select new role" />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        {option.icon}
                        {option.label}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleRoleChange}
              disabled={loading}
            >
              {loading ? 'Updating...' : 'Update Roles'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Users</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete {selectedCount} user{selectedCount > 1 ? 's' : ''}? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Users to be deleted:</h4>
            <div className="max-h-32 overflow-y-auto space-y-1">
              {selectedUsers.map((user) => (
                <div key={user.id} className="flex items-center justify-between text-sm p-2 bg-muted rounded">
                  <span>{user.full_name || user.email}</span>
                  <Badge variant="outline" className="text-xs">
                    {user.role}
                  </Badge>
                </div>
              ))}
            </div>
          </div>

          <AlertDialogFooter>
            <AlertDialogCancel disabled={loading}>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              disabled={loading}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {loading ? 'Deleting...' : 'Delete Users'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
