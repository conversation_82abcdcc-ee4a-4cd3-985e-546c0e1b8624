/**
 * Database Types for FSNC Dashboard
 * Auto-generated TypeScript interfaces based on Supabase PostgreSQL schema
 */

// =============================================================================
// ENUMS
// =============================================================================

/**
 * Audit action types for tracking system events
 */
export type AuditAction =
  | 'INSERT'
  | 'UPDATE'
  | 'DELETE'
  | 'LOGIN_SUCCESS'
  | 'LOGIN_FAIL'
  | 'INVITE_SENT'
  | 'INVITE_ACCEPTED'
  | 'NEWSLETTER_SUBSCRIBE_ATTEMPT'
  | 'NEWSLETTER_CONFIRMED'
  | 'NEWSLETTER_UNSUBSCRIBE';

/**
 * Content status for posts and pages
 */
export type ContentStatus =
  | 'draft'
  | 'pending_review'
  | 'rejected'
  | 'published'
  | 'archived';

/**
 * User roles within the system
 */
export type UserRole = 'admin' | 'publisher' | 'editor' | 'member';

// =============================================================================
// BASE INTERFACES
// =============================================================================

/**
 * Audit log for tracking system events and user actions
 */
export interface AuditLog {
  id: number;
  user_id?: string;
  action: AuditAction;
  target_table_name?: string;
  target_record_id?: string;
  old_value?: Record<string, any>;
  new_value?: Record<string, any>;
  description?: string;
  ip_address?: string;
  user_agent?: string;
  created_at?: Date;
}

/**
 * Biography entries for team members or featured individuals
 */
export interface Biography {
  id: string;
  full_name: string;
  title_role?: string;
  bio_text?: Record<string, any>;
  profile_image_url?: string;
  social_links?: Record<string, any>;
  display_order?: number;
  is_public?: boolean;
  created_by_id?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
  current_version?: number;
  last_edited_by_id?: string;
}

/**
 * Categories for organizing posts
 */
export interface Category {
  id: string;
  name: string;
  slug: string;
  description?: string;
  parent_id?: string;
  meta_title?: string;
  meta_description?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
  post_count?: number; // Computed field for analytics
}

/**
 * Invitations for user registration
 */
export interface Invitation {
  id: string;
  email: string;
  token: string;
  role_to_assign: UserRole;
  invited_by_id?: string;
  status: string;
  expires_at: Date;
  created_at?: Date;
  updated_at?: Date;
  resent_at?: Date;
  resend_count?: number;
}

/**
 * Newsletter subscribers
 */
export interface NewsletterSubscriber {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  platform?: string;
  is_active?: boolean;
  confirmation_token?: string;
  confirmation_token_expires_at?: Date;
  confirmed_at?: Date;
  subscribed_on_platform_at?: Date;
  source?: string;
  created_at?: Date;
  opt_out_at?: Date;
}

/**
 * Organization-wide settings
 */
export interface OrganizationSettings {
  key: string;
  value?: Record<string, any>;
  description?: string;
  updated_at?: Date;
}

/**
 * Static pages content
 */
export interface Page {
  id: string;
  title: string;
  slug: string;
  content?: Record<string, any>;
  author_id?: string;
  status: ContentStatus;
  published_at?: Date;
  meta_title?: string;
  meta_description?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
  current_version?: number;
  last_edited_by_id?: string;
}

/**
 * Blog posts
 */
export interface Post {
  id: string;
  title: string;
  slug: string;
  content?: Record<string, any>;
  excerpt?: string;
  featured_image_url?: string;
  author_id?: string;
  status: ContentStatus;
  published_at?: Date;
  category_id?: string;
  meta_title?: string;
  meta_description?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
  current_version?: number;
  last_edited_by_id?: string;
}

/**
 * Many-to-many relationship between posts and tags
 */
export interface PostTag {
  post_id: string;
  tag_id: string;
}

/**
 * Version history for posts
 */
export interface PostVersion {
  id: string;
  post_id: string;
  version_number: number;
  title: string;
  slug: string;
  content?: Record<string, any>;
  excerpt?: string;
  featured_image_url?: string;
  status: ContentStatus;
  published_at?: Date;
  category_id?: string;
  meta_title?: string;
  meta_description?: string;
  edited_by_id?: string;
  created_at?: Date;
  reason_for_change?: string;
}

/**
 * User profiles
 */
export interface Profile {
  id: string;
  email: string;
  full_name?: string;
  role: UserRole;
  avatar_url?: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
}

/**
 * Tags for categorizing posts
 */
export interface Tag {
  id: string;
  name: string;
  slug: string;
  created_at?: Date;
  updated_at?: Date;
  deleted_at?: Date;
}

// =============================================================================
// RELATIONSHIP TYPES
// =============================================================================

/**
 * Post with its related data
 */
export interface PostWithRelations extends Post {
  author?: Profile;
  category?: Category;
  tags?: Tag[];
  last_edited_by?: Profile;
}

/**
 * Biography with its related data
 */
export interface BiographyWithRelations extends Biography {
  created_by?: Profile;
  last_edited_by?: Profile;
}

/**
 * Page with its related data
 */
export interface PageWithRelations extends Page {
  author?: Profile;
  last_edited_by?: Profile;
}

/**
 * Invitation with its related data
 */
export interface InvitationWithRelations extends Invitation {
  invited_by?: Profile;
}

// =============================================================================
// UTILITY TYPES
// =============================================================================

/**
 * Insert types (omit auto-generated fields)
 */
export type InsertAuditLog = Omit<AuditLog, 'id' | 'created_at'>;
export type InsertBiography = Omit<
  Biography,
  'id' | 'created_at' | 'updated_at'
>;
export type InsertCategory = Omit<Category, 'id' | 'created_at' | 'updated_at'>;
export type InsertInvitation = Omit<
  Invitation,
  'id' | 'token' | 'created_at' | 'updated_at'
>;
export type InsertNewsletterSubscriber = Omit<
  NewsletterSubscriber,
  'id' | 'created_at'
>;
export type InsertOrganizationSettings = Omit<
  OrganizationSettings,
  'updated_at'
>;
export type InsertPage = Omit<Page, 'id' | 'created_at' | 'updated_at'>;
export type InsertPost = Omit<Post, 'id' | 'created_at' | 'updated_at'>;
export type InsertPostVersion = Omit<PostVersion, 'id' | 'created_at'>;
export type InsertProfile = Omit<Profile, 'created_at' | 'updated_at'>;
export type InsertTag = Omit<Tag, 'id' | 'created_at' | 'updated_at'>;

/**
 * Update types (all fields optional except id)
 */
export type UpdateBiography = Partial<Omit<Biography, 'id' | 'created_at'>> & {
  id: string;
};
export type UpdateCategory = Partial<Omit<Category, 'id' | 'created_at'>> & {
  id: string;
};
export type UpdateInvitation = Partial<
  Omit<Invitation, 'id' | 'created_at'>
> & { id: string };
export type UpdateNewsletterSubscriber = Partial<
  Omit<NewsletterSubscriber, 'id' | 'created_at'>
> & { id: string };
export type UpdateOrganizationSettings = Partial<
  Omit<OrganizationSettings, 'key'>
> & { key: string };
export type UpdatePage = Partial<Omit<Page, 'id' | 'created_at'>> & {
  id: string;
};
export type UpdatePost = Partial<Omit<Post, 'id' | 'created_at'>> & {
  id: string;
};
export type UpdateProfile = Partial<Omit<Profile, 'id' | 'created_at'>> & {
  id: string;
};
export type UpdateTag = Partial<Omit<Tag, 'id' | 'created_at'>> & {
  id: string;
};

/**
 * Database tables union type
 */
export type DatabaseTable =
  | 'audit_log'
  | 'biographies'
  | 'categories'
  | 'invitations'
  | 'newsletter_subscribers'
  | 'organization_settings'
  | 'pages'
  | 'posts'
  | 'post_tags'
  | 'post_versions'
  | 'profiles'
  | 'tags';

/**
 * Database schema type for Supabase client
 */
export interface Database {
  public: {
    Tables: {
      audit_log: {
        Row: AuditLog;
        Insert: InsertAuditLog;
        Update: Partial<InsertAuditLog>;
      };
      biographies: {
        Row: Biography;
        Insert: InsertBiography;
        Update: UpdateBiography;
      };
      categories: {
        Row: Category;
        Insert: InsertCategory;
        Update: UpdateCategory;
      };
      invitations: {
        Row: Invitation;
        Insert: InsertInvitation;
        Update: UpdateInvitation;
      };
      newsletter_subscribers: {
        Row: NewsletterSubscriber;
        Insert: InsertNewsletterSubscriber;
        Update: UpdateNewsletterSubscriber;
      };
      organization_settings: {
        Row: OrganizationSettings;
        Insert: InsertOrganizationSettings;
        Update: UpdateOrganizationSettings;
      };
      pages: { Row: Page; Insert: InsertPage; Update: UpdatePage };
      posts: { Row: Post; Insert: InsertPost; Update: UpdatePost };
      post_tags: { Row: PostTag; Insert: PostTag; Update: never };
      post_versions: {
        Row: PostVersion;
        Insert: InsertPostVersion;
        Update: never;
      };
      profiles: { Row: Profile; Insert: InsertProfile; Update: UpdateProfile };
      tags: { Row: Tag; Insert: InsertTag; Update: UpdateTag };
    };
    Views: {};
    Functions: {};
    Enums: {
      audit_action: AuditAction;
      content_status: ContentStatus;
      user_role: UserRole;
    };
  };
}

/**
 * Settings-specific types for better type safety
 */
export interface SettingsFormData {
  [key: string]: any;
}

export interface SettingsLoadingState {
  settings: boolean;
  saving: boolean;
}

export interface SettingsErrorState {
  general?: string;
  [key: string]: string | undefined;
}
