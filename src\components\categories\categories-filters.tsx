'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  Search,
  Filter,
  X,
  ChevronDown,
  Calendar,
  Hash,
  FileText,
} from 'lucide-react';
import type { Category } from '@/types';

interface CategoryFilters {
  search: string;
  parentCategory: string;
  hasDescription: string;
  postCountMin: string;
  postCountMax: string;
  dateFrom: string;
  dateTo: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface CategoriesFiltersProps {
  filters: CategoryFilters;
  onFiltersChange: (filters: Partial<CategoryFilters>) => void;
  onReset: () => void;
  categories?: Category[];
  loading?: boolean;
}

export function CategoriesFilters({
  filters,
  onFiltersChange,
  onReset,
  categories = [],
  loading = false,
}: CategoriesFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const activeFiltersCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'sortBy' || key === 'sortOrder') return false;
    return value && value !== 'all' && value !== '';
  }).length;

  const parentCategories = categories.filter(cat => !cat.parent_id);

  const handleFilterChange = (key: keyof CategoryFilters, value: string) => {
    onFiltersChange({ [key]: value });
  };

  const clearFilter = (key: keyof CategoryFilters) => {
    onFiltersChange({ [key]: '' });
  };

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFiltersCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {activeFiltersCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onReset}
                className="h-8 px-2"
              >
                <X className="h-3 w-3 mr-1" />
                Clear
              </Button>
            )}
            <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 px-2">
                  <ChevronDown className={`h-3 w-3 transition-transform ${
                    isExpanded ? 'rotate-180' : ''
                  }`} />
                </Button>
              </CollapsibleTrigger>
            </Collapsible>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search categories..."
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            className="pl-9"
            disabled={loading}
          />
          {filters.search && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              onClick={() => clearFilter('search')}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant={filters.hasDescription === 'true' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFilterChange('hasDescription', 
              filters.hasDescription === 'true' ? '' : 'true'
            )}
          >
            <FileText className="h-3 w-3 mr-1" />
            Has Description
          </Button>
          <Button
            variant={filters.parentCategory === 'none' ? 'default' : 'outline'}
            size="sm"
            onClick={() => handleFilterChange('parentCategory', 
              filters.parentCategory === 'none' ? '' : 'none'
            )}
          >
            <Hash className="h-3 w-3 mr-1" />
            Top Level
          </Button>
        </div>

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-4">
            {/* Advanced Filters */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Parent Category */}
              <div className="space-y-2">
                <Label htmlFor="parent-category">Parent Category</Label>
                <Select
                  value={filters.parentCategory}
                  onValueChange={(value) => handleFilterChange('parentCategory', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All categories</SelectItem>
                    <SelectItem value="none">Top level only</SelectItem>
                    {parentCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Post Count Range */}
              <div className="space-y-2">
                <Label>Post Count Range</Label>
                <div className="flex gap-2">
                  <Input
                    type="number"
                    placeholder="Min"
                    value={filters.postCountMin}
                    onChange={(e) => handleFilterChange('postCountMin', e.target.value)}
                    className="w-20"
                  />
                  <span className="self-center text-muted-foreground">to</span>
                  <Input
                    type="number"
                    placeholder="Max"
                    value={filters.postCountMax}
                    onChange={(e) => handleFilterChange('postCountMax', e.target.value)}
                    className="w-20"
                  />
                </div>
              </div>

              {/* Sort Options */}
              <div className="space-y-2">
                <Label htmlFor="sort-by">Sort By</Label>
                <div className="flex gap-2">
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value) => handleFilterChange('sortBy', value)}
                  >
                    <SelectTrigger className="flex-1">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="created_at">Created Date</SelectItem>
                      <SelectItem value="updated_at">Updated Date</SelectItem>
                      <SelectItem value="post_count">Post Count</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select
                    value={filters.sortOrder}
                    onValueChange={(value: 'asc' | 'desc') => 
                      handleFilterChange('sortOrder', value)
                    }
                  >
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="asc">↑</SelectItem>
                      <SelectItem value="desc">↓</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Date Range */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date-from">Created From</Label>
                <Input
                  id="date-from"
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="date-to">Created To</Label>
                <Input
                  id="date-to"
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => handleFilterChange('dateTo', e.target.value)}
                />
              </div>
            </div>
          </CollapsibleContent>
        </Collapsible>

        {/* Active Filters */}
        {activeFiltersCount > 0 && (
          <div className="flex flex-wrap gap-2 pt-2 border-t">
            {filters.search && (
              <Badge variant="secondary" className="gap-1">
                Search: {filters.search}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => clearFilter('search')}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
            {filters.parentCategory && filters.parentCategory !== 'all' && (
              <Badge variant="secondary" className="gap-1">
                Parent: {filters.parentCategory === 'none' ? 'Top level' : 
                  categories.find(c => c.id === filters.parentCategory)?.name || 'Unknown'}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => clearFilter('parentCategory')}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
            {(filters.postCountMin || filters.postCountMax) && (
              <Badge variant="secondary" className="gap-1">
                Posts: {filters.postCountMin || '0'}-{filters.postCountMax || '∞'}
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-4 w-4 p-0 hover:bg-transparent"
                  onClick={() => {
                    clearFilter('postCountMin');
                    clearFilter('postCountMax');
                  }}
                >
                  <X className="h-2 w-2" />
                </Button>
              </Badge>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
