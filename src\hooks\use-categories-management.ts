'use client';

import { useState, useEffect, useCallback } from 'react';
import { getCategories, createCategory, updateCategory, deleteCategory } from '@/lib/database-helpers';
import type { Category, InsertCategory, UpdateCategory } from '@/types';

interface CategoryFilters {
  search: string;
  parentCategory: string;
  hasDescription: string;
  postCountMin: string;
  postCountMax: string;
  dateFrom: string;
  dateTo: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface UseCategoriesManagementReturn {
  categories: Category[];
  filteredCategories: Category[];
  loading: {
    categories: boolean;
    actions: boolean;
  };
  errors: {
    categories: string | null;
    actions: string | null;
  };
  filters: CategoryFilters;
  updateFilters: (newFilters: Partial<CategoryFilters>) => void;
  resetFilters: () => void;
  refresh: () => Promise<void>;
  createNewCategory: (categoryData: InsertCategory) => Promise<{ success: boolean; data?: Category; error?: string }>;
  updateExistingCategory: (id: string, categoryData: UpdateCategory) => Promise<{ success: boolean; data?: Category; error?: string }>;
  deleteCategories: (categories: Category[]) => Promise<void>;
  duplicateCategories: (categories: Category[]) => Promise<void>;
  exportCategories: (categories: Category[]) => Promise<void>;
}

const defaultFilters: CategoryFilters = {
  search: '',
  parentCategory: '',
  hasDescription: '',
  postCountMin: '',
  postCountMax: '',
  dateFrom: '',
  dateTo: '',
  sortBy: 'name',
  sortOrder: 'asc',
};

export function useCategoriesManagement(): UseCategoriesManagementReturn {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState({
    categories: true,
    actions: false,
  });
  const [errors, setErrors] = useState({
    categories: null as string | null,
    actions: null as string | null,
  });
  const [filters, setFilters] = useState<CategoryFilters>(defaultFilters);

  // Fetch categories
  const fetchCategories = useCallback(async () => {
    setLoading(prev => ({ ...prev, categories: true }));
    setErrors(prev => ({ ...prev, categories: null }));

    try {
      const { data, error } = await getCategories();
      if (error) {
        throw new Error(error);
      }
      setCategories(data || []);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch categories';
      setErrors(prev => ({ ...prev, categories: errorMessage }));
      console.error('Error fetching categories:', error);
    } finally {
      setLoading(prev => ({ ...prev, categories: false }));
    }
  }, []);

  // Filter and sort categories
  const filteredCategories = useCallback(() => {
    let filtered = [...categories];

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(category =>
        category.name.toLowerCase().includes(searchLower) ||
        category.slug.toLowerCase().includes(searchLower) ||
        (category.description && category.description.toLowerCase().includes(searchLower))
      );
    }

    // Apply parent category filter
    if (filters.parentCategory) {
      if (filters.parentCategory === 'none') {
        filtered = filtered.filter(category => !category.parent_id);
      } else {
        filtered = filtered.filter(category => category.parent_id === filters.parentCategory);
      }
    }

    // Apply description filter
    if (filters.hasDescription === 'true') {
      filtered = filtered.filter(category => category.description?.trim());
    } else if (filters.hasDescription === 'false') {
      filtered = filtered.filter(category => !category.description?.trim());
    }

    // Apply post count filters
    if (filters.postCountMin) {
      const min = parseInt(filters.postCountMin);
      filtered = filtered.filter(category => (category.post_count || 0) >= min);
    }
    if (filters.postCountMax) {
      const max = parseInt(filters.postCountMax);
      filtered = filtered.filter(category => (category.post_count || 0) <= max);
    }

    // Apply date filters
    if (filters.dateFrom) {
      const fromDate = new Date(filters.dateFrom);
      filtered = filtered.filter(category => new Date(category.created_at) >= fromDate);
    }
    if (filters.dateTo) {
      const toDate = new Date(filters.dateTo);
      toDate.setHours(23, 59, 59, 999); // End of day
      filtered = filtered.filter(category => new Date(category.created_at) <= toDate);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any = a[filters.sortBy as keyof Category];
      let bValue: any = b[filters.sortBy as keyof Category];

      // Handle different data types
      if (filters.sortBy === 'post_count') {
        aValue = aValue || 0;
        bValue = bValue || 0;
      } else if (filters.sortBy === 'created_at' || filters.sortBy === 'updated_at') {
        aValue = new Date(aValue);
        bValue = new Date(bValue);
      } else {
        aValue = String(aValue || '').toLowerCase();
        bValue = String(bValue || '').toLowerCase();
      }

      if (aValue < bValue) return filters.sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return filters.sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [categories, filters]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<CategoryFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Reset filters
  const resetFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  // Create category
  const createNewCategory = useCallback(async (categoryData: InsertCategory) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      const { data, error } = await createCategory(categoryData);
      if (error) {
        throw new Error(error);
      }

      // Refresh categories list
      await fetchCategories();
      
      return { success: true, data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to create category';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      return { success: false, error: errorMessage };
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchCategories]);

  // Update category
  const updateExistingCategory = useCallback(async (id: string, categoryData: UpdateCategory) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      const { data, error } = await updateCategory(id, categoryData);
      if (error) {
        throw new Error(error);
      }

      // Refresh categories list
      await fetchCategories();
      
      return { success: true, data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to update category';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      return { success: false, error: errorMessage };
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchCategories]);

  // Delete categories
  const deleteCategories = useCallback(async (categoriesToDelete: Category[]) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      for (const category of categoriesToDelete) {
        const { error } = await deleteCategory(category.id);
        if (error) {
          throw new Error(`Failed to delete ${category.name}: ${error}`);
        }
      }

      // Refresh categories list
      await fetchCategories();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete categories';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchCategories]);

  // Duplicate categories
  const duplicateCategories = useCallback(async (categoriesToDuplicate: Category[]) => {
    setLoading(prev => ({ ...prev, actions: true }));
    setErrors(prev => ({ ...prev, actions: null }));

    try {
      for (const category of categoriesToDuplicate) {
        const duplicateData: InsertCategory = {
          name: `${category.name} (Copy)`,
          slug: `${category.slug}-copy-${Date.now()}`,
          description: category.description,
          parent_id: category.parent_id,
          meta_title: category.meta_title,
          meta_description: category.meta_description,
        };

        const { error } = await createCategory(duplicateData);
        if (error) {
          throw new Error(`Failed to duplicate ${category.name}: ${error}`);
        }
      }

      // Refresh categories list
      await fetchCategories();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to duplicate categories';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      throw error;
    } finally {
      setLoading(prev => ({ ...prev, actions: false }));
    }
  }, [fetchCategories]);

  // Export categories
  const exportCategories = useCallback(async (categoriesToExport: Category[]) => {
    try {
      const exportData = categoriesToExport.map(category => ({
        name: category.name,
        slug: category.slug,
        description: category.description,
        parent_category: categories.find(c => c.id === category.parent_id)?.name || '',
        post_count: category.post_count || 0,
        created_at: category.created_at,
        updated_at: category.updated_at,
      }));

      const blob = new Blob([JSON.stringify(exportData, null, 2)], {
        type: 'application/json',
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `categories-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to export categories';
      setErrors(prev => ({ ...prev, actions: errorMessage }));
      throw error;
    }
  }, [categories]);

  // Initial load
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    filteredCategories: filteredCategories(),
    loading,
    errors,
    filters,
    updateFilters,
    resetFilters,
    refresh: fetchCategories,
    createNewCategory,
    updateExistingCategory,
    deleteCategories,
    duplicateCategories,
    exportCategories,
  };
}
