'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { usePagesManagement } from '@/hooks/use-pages-management';
import { PageTree, PageTemplates, ContentScheduler } from '@/components/pages';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  FileText,
  Plus,
  Search,
  RefreshCw,
  AlertTriangle,
  Eye,
  Edit,
  Copy,
  Trash2,
  Calendar,
  User,
  Globe,
  Layout,
  Settings,
  TreePine,
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import type { ContentStatus, QueryParams } from '@/types';

export default function WebPagesPage() {
  const router = useRouter();
  const {
    pages,
    authors,
    pagination,
    loading,
    errors,
    loadPages,
    deletePage,
    duplicatePage,
    refresh,
  } = usePagesManagement();

  const [activeTab, setActiveTab] = useState('list');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<ContentStatus | 'all'>(
    'all'
  );
  const [authorFilter, setAuthorFilter] = useState<string>('all');

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    const params: QueryParams = {
      search: query || undefined,
      status: statusFilter !== 'all' ? statusFilter : undefined,
      author: authorFilter !== 'all' ? authorFilter : undefined,
    };
    loadPages(params);
  };

  const handleStatusFilter = (status: ContentStatus | 'all') => {
    setStatusFilter(status);
    const params: QueryParams = {
      search: searchQuery || undefined,
      status: status !== 'all' ? status : undefined,
      author: authorFilter !== 'all' ? authorFilter : undefined,
    };
    loadPages(params);
  };

  const handleAuthorFilter = (author: string) => {
    setAuthorFilter(author);
    const params: QueryParams = {
      search: searchQuery || undefined,
      status: statusFilter !== 'all' ? statusFilter : undefined,
      author: author !== 'all' ? author : undefined,
    };
    loadPages(params);
  };

  const handleEdit = (pageId: string) => {
    router.push(`/web-pages/${pageId}/edit`);
  };

  const handlePreview = (pageSlug: string) => {
    // Open preview in new tab
    window.open(`/preview/page/${pageSlug}`, '_blank');
  };

  const handleDuplicate = async (pageId: string) => {
    const result = await duplicatePage(pageId);
    if (result.success) {
      // Optionally show success message
    }
  };

  const handleDelete = async (pageId: string) => {
    if (confirm('Are you sure you want to delete this page?')) {
      const result = await deletePage(pageId);
      if (result.success) {
        // Optionally show success message
      }
    }
  };

  const handleSelectTemplate = (template: any) => {
    // TODO: Implement template preview
    console.log('Preview template:', template);
  };

  const handleCreateFromTemplate = (template: any) => {
    // Navigate to new page with template data
    const templateData = {
      title: template.name,
      content: template.content,
      meta_title: template.meta_title,
      meta_description: template.meta_description,
    };

    // Store template data in sessionStorage for the new page form
    sessionStorage.setItem('pageTemplate', JSON.stringify(templateData));
    router.push('/web-pages/new');
  };

  const handleAddChild = (parentId: string) => {
    // Navigate to new page with parent ID
    const params = new URLSearchParams();
    if (parentId) {
      params.set('parent', parentId);
    }
    router.push(`/web-pages/new?${params.toString()}`);
  };

  const handleReorder = (
    pageId: string,
    newParentId?: string,
    newOrder?: number
  ) => {
    // TODO: Implement page reordering
    console.log('Reorder page:', { pageId, newParentId, newOrder });
  };

  const getStatusBadge = (status: ContentStatus) => {
    const variants = {
      draft: 'secondary',
      pending_review: 'outline',
      published: 'default',
      archived: 'destructive',
      rejected: 'destructive',
    } as const;

    return (
      <Badge variant={variants[status] || 'secondary'}>
        {status.replace('_', ' ')}
      </Badge>
    );
  };

  return (
    <div className="flex flex-1 flex-col gap-4 p-4 pt-0">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pages</h1>
          <p className="text-muted-foreground">
            Manage your website&apos;s static pages and content
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refresh}
            disabled={loading.pages}
          >
            <RefreshCw
              className={`h-4 w-4 mr-2 ${loading.pages ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Button onClick={() => router.push('/web-pages/new')}>
            <Plus className="h-4 w-4 mr-2" />
            New Page
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {errors.pages && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{errors.pages}</AlertDescription>
        </Alert>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="list">
            <FileText className="h-4 w-4 mr-2" />
            Pages List
          </TabsTrigger>
          <TabsTrigger value="tree">
            <TreePine className="h-4 w-4 mr-2" />
            Page Structure
          </TabsTrigger>
          <TabsTrigger value="templates">
            <Layout className="h-4 w-4 mr-2" />
            Templates
          </TabsTrigger>
          <TabsTrigger value="analytics">
            <Settings className="h-4 w-4 mr-2" />
            Scheduler
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6 mt-6">
          {/* Filters */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Filters</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col gap-4 md:flex-row md:items-center">
                {/* Search */}
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                    <Input
                      placeholder="Search pages..."
                      value={searchQuery}
                      onChange={(e) => handleSearch(e.target.value)}
                      className="pl-9"
                    />
                  </div>
                </div>

                {/* Status Filter */}
                <Select value={statusFilter} onValueChange={handleStatusFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="pending_review">
                      Pending Review
                    </SelectItem>
                    <SelectItem value="published">Published</SelectItem>
                    <SelectItem value="archived">Archived</SelectItem>
                  </SelectContent>
                </Select>

                {/* Author Filter */}
                <Select value={authorFilter} onValueChange={handleAuthorFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by author" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Authors</SelectItem>
                    {authors.map((author) => (
                      <SelectItem key={author.id} value={author.id}>
                        {author.full_name || author.email}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Pages Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Pages
                <Badge variant="secondary">{pages.length}</Badge>
              </CardTitle>
              <CardDescription>
                Manage your website&apos;s static pages with advanced filtering
                and bulk operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              {loading.pages ? (
                <div className="flex items-center justify-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin" />
                  <span className="ml-2">Loading pages...</span>
                </div>
              ) : pages.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No pages found</h3>
                  <p className="text-muted-foreground mb-4">
                    {searchQuery ||
                    statusFilter !== 'all' ||
                    authorFilter !== 'all'
                      ? 'No pages match your current filters.'
                      : 'Get started by creating your first page.'}
                  </p>
                  <Button onClick={() => router.push('/web-pages/new')}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Page
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Pages Grid */}
                  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {pages.map((page) => (
                      <Card
                        key={page.id}
                        className="hover:shadow-md transition-shadow"
                      >
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <CardTitle className="text-lg truncate">
                                {page.title}
                              </CardTitle>
                              <CardDescription className="flex items-center gap-2 mt-1">
                                <Globe className="h-3 w-3" />/{page.slug}
                              </CardDescription>
                            </div>
                            {getStatusBadge(page.status)}
                          </div>
                        </CardHeader>
                        <CardContent className="pt-0">
                          <div className="space-y-3">
                            {/* Meta Info */}
                            <div className="text-sm text-muted-foreground space-y-1">
                              {page.author && (
                                <div className="flex items-center gap-2">
                                  <User className="h-3 w-3" />
                                  <span>
                                    {page.author.full_name || page.author.email}
                                  </span>
                                </div>
                              )}
                              <div className="flex items-center gap-2">
                                <Calendar className="h-3 w-3" />
                                <span>
                                  {page.updated_at
                                    ? `Updated ${formatDistanceToNow(
                                        new Date(page.updated_at)
                                      )} ago`
                                    : 'Never updated'}
                                </span>
                              </div>
                            </div>

                            {/* Meta Description Preview */}
                            {page.meta_description && (
                              <p className="text-sm text-muted-foreground line-clamp-2">
                                {page.meta_description}
                              </p>
                            )}

                            {/* Actions */}
                            <div className="flex items-center gap-2 pt-2 border-t">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleEdit(page.id)}
                              >
                                <Edit className="h-3 w-3 mr-1" />
                                Edit
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handlePreview(page.slug)}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                Preview
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDuplicate(page.id)}
                              >
                                <Copy className="h-3 w-3 mr-1" />
                                Duplicate
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleDelete(page.id)}
                                className="text-destructive hover:text-destructive"
                              >
                                <Trash2 className="h-3 w-3 mr-1" />
                                Delete
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {/* Pagination */}
                  {pagination && pagination.totalPages > 1 && (
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-sm text-muted-foreground">
                        Showing {(pagination.page - 1) * pagination.limit + 1}{' '}
                        to{' '}
                        {Math.min(
                          pagination.page * pagination.limit,
                          pagination.total
                        )}{' '}
                        of {pagination.total} pages
                      </div>
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            loadPages({
                              ...{
                                search: searchQuery || undefined,
                                status:
                                  statusFilter !== 'all'
                                    ? statusFilter
                                    : undefined,
                                author:
                                  authorFilter !== 'all'
                                    ? authorFilter
                                    : undefined,
                              },
                              page: pagination.page - 1,
                            })
                          }
                          disabled={!pagination.hasPrev}
                        >
                          Previous
                        </Button>
                        <span className="text-sm">
                          Page {pagination.page} of {pagination.totalPages}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            loadPages({
                              ...{
                                search: searchQuery || undefined,
                                status:
                                  statusFilter !== 'all'
                                    ? statusFilter
                                    : undefined,
                                author:
                                  authorFilter !== 'all'
                                    ? authorFilter
                                    : undefined,
                              },
                              page: pagination.page + 1,
                            })
                          }
                          disabled={!pagination.hasNext}
                        >
                          Next
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions Panel */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layout className="h-5 w-5" />
                Quick Actions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/web-pages/new')}
                >
                  <Plus className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">New Page</div>
                    <div className="text-xs text-muted-foreground">
                      Create a new static page
                    </div>
                  </div>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/web-pages/templates')}
                >
                  <Layout className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">Templates</div>
                    <div className="text-xs text-muted-foreground">
                      Manage page templates
                    </div>
                  </div>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/web-pages/seo')}
                >
                  <Settings className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">SEO Tools</div>
                    <div className="text-xs text-muted-foreground">
                      Optimize page SEO
                    </div>
                  </div>
                </Button>
                <Button
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center gap-2"
                  onClick={() => router.push('/web-pages/analytics')}
                >
                  <Eye className="h-6 w-6" />
                  <div className="text-center">
                    <div className="font-medium">Analytics</div>
                    <div className="text-xs text-muted-foreground">
                      View page performance
                    </div>
                  </div>
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tree" className="space-y-6 mt-6">
          <PageTree
            pages={pages}
            onEdit={handleEdit}
            onPreview={handlePreview}
            onDelete={handleDelete}
            onAddChild={handleAddChild}
            onReorder={handleReorder}
          />
        </TabsContent>

        <TabsContent value="templates" className="space-y-6 mt-6">
          <PageTemplates
            onSelectTemplate={handleSelectTemplate}
            onCreateFromTemplate={handleCreateFromTemplate}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6 mt-6">
          <ContentScheduler
            pages={pages}
            onEdit={handleEdit}
            onPreview={handlePreview}
            onReschedule={(pageId, newDate) => {
              // TODO: Implement rescheduling
              console.log('Reschedule page:', pageId, 'to:', newDate);
            }}
          />
        </TabsContent>
      </Tabs>
    </div>
  );
}
