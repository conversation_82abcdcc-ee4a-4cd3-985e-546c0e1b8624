'use client';

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>riangle } from 'lucide-react';
import type { Tag } from '@/types';

interface TagWithStats extends Tag {
  post_count?: number;
  usage_trend?: 'up' | 'down' | 'stable';
}

interface TagAnalyticsProps {
  tags: TagWithStats[];
}

export function TagAnalytics({ tags }: TagAnalyticsProps) {
  const totalTags = tags.length;
  const usedTags = tags.filter(tag => (tag.post_count || 0) > 0);
  const unusedTags = tags.filter(tag => (tag.post_count || 0) === 0);
  const totalUsage = tags.reduce((sum, tag) => sum + (tag.post_count || 0), 0);

  const mostUsedTags = tags
    .filter(tag => (tag.post_count || 0) > 0)
    .sort((a, b) => (b.post_count || 0) - (a.post_count || 0))
    .slice(0, 10);

  const maxUsage = Math.max(...tags.map(tag => tag.post_count || 0), 1);

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Hash className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Tags</p>
                <p className="text-2xl font-bold">{totalTags}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <TrendingUp className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Used Tags</p>
                <p className="text-2xl font-bold">{usedTags.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <BarChart3 className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Usage</p>
                <p className="text-2xl font-bold">{totalUsage}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Unused Tags</p>
                <p className="text-2xl font-bold">{unusedTags.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Most Used Tags */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Most Used Tags
          </CardTitle>
        </CardHeader>
        <CardContent>
          {mostUsedTags.length > 0 ? (
            <div className="space-y-3">
              {mostUsedTags.map((tag, index) => (
                <div key={tag.id} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">#{index + 1}</span>
                      <span className="text-sm truncate">{tag.name}</span>
                    </div>
                    <Badge variant="secondary">
                      {tag.post_count} posts
                    </Badge>
                  </div>
                  <Progress 
                    value={(tag.post_count || 0) / maxUsage * 100} 
                    className="h-2"
                  />
                </div>
              ))}
            </div>
          ) : (
            <p className="text-sm text-muted-foreground text-center py-4">
              No tags with posts yet
            </p>
          )}
        </CardContent>
      </Card>

      {/* Unused Tags Warning */}
      {unusedTags.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-5 w-5" />
              Unused Tags
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-yellow-700 mb-3">
              You have {unusedTags.length} tags that aren't being used by any posts. 
              Consider removing them or creating content with these tags.
            </p>
            <div className="flex flex-wrap gap-2">
              {unusedTags.slice(0, 10).map((tag) => (
                <Badge key={tag.id} variant="outline" className="text-yellow-700">
                  {tag.name}
                </Badge>
              ))}
              {unusedTags.length > 10 && (
                <Badge variant="outline" className="text-yellow-700">
                  +{unusedTags.length - 10} more
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
