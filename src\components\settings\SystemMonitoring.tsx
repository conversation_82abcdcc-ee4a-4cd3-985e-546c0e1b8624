'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Activity, 
  Database, 
  HardDrive, 
  AlertTriangle, 
  CheckCircle,
  Clock,
  Trash2,
  Download,
  Upload,
  RefreshCw,
  Settings,
  Zap
} from 'lucide-react';

interface SystemMonitoringProps {
  settings: Record<string, any>;
  loading: { settings: boolean; saving: boolean };
  errors: Record<string, string | undefined>;
  onUpdateSetting: (key: string, value: any) => void;
}

export function SystemMonitoring({ 
  settings, 
  loading, 
  errors, 
  onUpdateSetting 
}: SystemMonitoringProps) {
  const [systemStatus, setSystemStatus] = useState({
    database: 'healthy',
    storage: 'healthy',
    api: 'healthy',
    memory: 85,
    cpu: 45,
    disk: 62,
  });

  const handleMonitoringChange = (key: string, value: any) => {
    const currentMonitoring = settings.system_monitoring || {};
    onUpdateSetting('system_monitoring', {
      ...currentMonitoring,
      [key]: value,
    });
  };

  const handleBackupChange = (key: string, value: any) => {
    const currentBackup = settings.backup_settings || {};
    onUpdateSetting('backup_settings', {
      ...currentBackup,
      [key]: value,
    });
  };

  const handleMaintenanceChange = (key: string, value: any) => {
    const currentMaintenance = settings.maintenance_settings || {};
    onUpdateSetting('maintenance_settings', {
      ...currentMaintenance,
      [key]: value,
    });
  };

  const handleErrorLoggingChange = (key: string, value: any) => {
    const currentLogging = settings.error_logging || {};
    onUpdateSetting('error_logging', {
      ...currentLogging,
      [key]: value,
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'text-green-600';
      case 'warning': return 'text-yellow-600';
      case 'error': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'error': return <AlertTriangle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* System Health Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            System Health Overview
          </CardTitle>
          <CardDescription>
            Real-time monitoring of system components and performance
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  <span className="font-medium">Database</span>
                </div>
                <div className={`flex items-center gap-1 ${getStatusColor(systemStatus.database)}`}>
                  {getStatusIcon(systemStatus.database)}
                  <span className="text-sm capitalize">{systemStatus.database}</span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">Connection stable, queries optimized</p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <HardDrive className="h-4 w-4" />
                  <span className="font-medium">Storage</span>
                </div>
                <div className={`flex items-center gap-1 ${getStatusColor(systemStatus.storage)}`}>
                  {getStatusIcon(systemStatus.storage)}
                  <span className="text-sm capitalize">{systemStatus.storage}</span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">62% used, 2.1GB available</p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">API</span>
                </div>
                <div className={`flex items-center gap-1 ${getStatusColor(systemStatus.api)}`}>
                  {getStatusIcon(systemStatus.api)}
                  <span className="text-sm capitalize">{systemStatus.api}</span>
                </div>
              </div>
              <p className="text-xs text-muted-foreground">Response time: 120ms avg</p>
            </div>
          </div>

          <div className="space-y-3">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Memory Usage</span>
                <span>{systemStatus.memory}%</span>
              </div>
              <Progress value={systemStatus.memory} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>CPU Usage</span>
                <span>{systemStatus.cpu}%</span>
              </div>
              <Progress value={systemStatus.cpu} className="h-2" />
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Disk Usage</span>
                <span>{systemStatus.disk}%</span>
              </div>
              <Progress value={systemStatus.disk} className="h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Performance Monitoring */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Performance Monitoring
          </CardTitle>
          <CardDescription>
            Configure system performance monitoring and alerts
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Performance Monitoring</Label>
              <p className="text-sm text-muted-foreground">
                Monitor system performance and send alerts
              </p>
            </div>
            <Switch
              checked={settings.system_monitoring?.enabled || true}
              onCheckedChange={(checked) => handleMonitoringChange('enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.system_monitoring?.enabled && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cpu-threshold">CPU Alert Threshold (%)</Label>
                  <Input
                    id="cpu-threshold"
                    type="number"
                    value={settings.system_monitoring?.cpu_threshold || 80}
                    onChange={(e) => handleMonitoringChange('cpu_threshold', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="50"
                    max="95"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="memory-threshold">Memory Alert Threshold (%)</Label>
                  <Input
                    id="memory-threshold"
                    type="number"
                    value={settings.system_monitoring?.memory_threshold || 85}
                    onChange={(e) => handleMonitoringChange('memory_threshold', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="50"
                    max="95"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="disk-threshold">Disk Alert Threshold (%)</Label>
                  <Input
                    id="disk-threshold"
                    type="number"
                    value={settings.system_monitoring?.disk_threshold || 90}
                    onChange={(e) => handleMonitoringChange('disk_threshold', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="70"
                    max="95"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="monitoring-interval">Monitoring Interval (minutes)</Label>
                <Select
                  value={settings.system_monitoring?.interval_minutes?.toString() || '5'}
                  onValueChange={(value) => handleMonitoringChange('interval_minutes', parseInt(value))}
                  disabled={loading.saving}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select interval" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 minute</SelectItem>
                    <SelectItem value="5">5 minutes</SelectItem>
                    <SelectItem value="15">15 minutes</SelectItem>
                    <SelectItem value="30">30 minutes</SelectItem>
                    <SelectItem value="60">1 hour</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Database Optimization */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Database Optimization & Cleanup
          </CardTitle>
          <CardDescription>
            Tools for maintaining database performance and cleanliness
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
              <div className="flex items-center gap-2 mb-2">
                <RefreshCw className="h-4 w-4" />
                <span className="font-medium">Optimize Database</span>
              </div>
              <p className="text-xs text-muted-foreground text-left">
                Analyze and optimize database queries and indexes
              </p>
            </Button>

            <Button variant="outline" className="h-auto p-4 flex flex-col items-start">
              <div className="flex items-center gap-2 mb-2">
                <Trash2 className="h-4 w-4" />
                <span className="font-medium">Clean Audit Logs</span>
              </div>
              <p className="text-xs text-muted-foreground text-left">
                Remove old audit log entries to free up space
              </p>
            </Button>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Auto-cleanup Old Data</Label>
              <p className="text-sm text-muted-foreground">
                Automatically remove old logs and temporary data
              </p>
            </div>
            <Switch
              checked={settings.system_monitoring?.auto_cleanup || true}
              onCheckedChange={(checked) => handleMonitoringChange('auto_cleanup', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.system_monitoring?.auto_cleanup && (
            <div className="space-y-2">
              <Label htmlFor="cleanup-days">Keep Data For (days)</Label>
              <Input
                id="cleanup-days"
                type="number"
                value={settings.system_monitoring?.cleanup_after_days || 90}
                onChange={(e) => handleMonitoringChange('cleanup_after_days', parseInt(e.target.value))}
                disabled={loading.saving}
                min="30"
                max="365"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {/* Backup Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Backup & Restore Management
          </CardTitle>
          <CardDescription>
            Configure automated backups and restore procedures
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Automated Backups</Label>
              <p className="text-sm text-muted-foreground">
                Automatically create system backups on schedule
              </p>
            </div>
            <Switch
              checked={settings.backup_settings?.enabled || true}
              onCheckedChange={(checked) => handleBackupChange('enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.backup_settings?.enabled && (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="backup-frequency">Backup Frequency</Label>
                  <Select
                    value={settings.backup_settings?.frequency || 'daily'}
                    onValueChange={(value) => handleBackupChange('frequency', value)}
                    disabled={loading.saving}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select frequency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="backup-retention">Retention Period (days)</Label>
                  <Input
                    id="backup-retention"
                    type="number"
                    value={settings.backup_settings?.retention_days || 30}
                    onChange={(e) => handleBackupChange('retention_days', parseInt(e.target.value))}
                    disabled={loading.saving}
                    min="7"
                    max="365"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <Label>Last Backup Status</Label>
                <div className="p-3 border rounded-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm">Backup completed successfully</span>
                    </div>
                    <Badge variant="secondary">2 hours ago</Badge>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Size: 245 MB | Duration: 3m 42s
                  </p>
                </div>
              </div>

              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Create Backup Now
                </Button>
                <Button variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Restore from Backup
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Error Logging & Debugging */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Error Logging & Debugging
          </CardTitle>
          <CardDescription>
            Configure error tracking and debugging tools
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Error Logging</Label>
              <p className="text-sm text-muted-foreground">
                Log system errors and exceptions for debugging
              </p>
            </div>
            <Switch
              checked={settings.error_logging?.enabled || true}
              onCheckedChange={(checked) => handleErrorLoggingChange('enabled', checked)}
              disabled={loading.saving}
            />
          </div>

          {settings.error_logging?.enabled && (
            <>
              <div className="space-y-2">
                <Label htmlFor="log-level">Log Level</Label>
                <Select
                  value={settings.error_logging?.level || 'error'}
                  onValueChange={(value) => handleErrorLoggingChange('level', value)}
                  disabled={loading.saving}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select log level" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="debug">Debug (All messages)</SelectItem>
                    <SelectItem value="info">Info (Informational and above)</SelectItem>
                    <SelectItem value="warn">Warning (Warnings and above)</SelectItem>
                    <SelectItem value="error">Error (Errors only)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="log-retention">Log Retention (days)</Label>
                <Input
                  id="log-retention"
                  type="number"
                  value={settings.error_logging?.retention_days || 30}
                  onChange={(e) => handleErrorLoggingChange('retention_days', parseInt(e.target.value))}
                  disabled={loading.saving}
                  min="7"
                  max="90"
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Email Alerts for Critical Errors</Label>
                  <p className="text-sm text-muted-foreground">
                    Send email notifications for critical system errors
                  </p>
                </div>
                <Switch
                  checked={settings.error_logging?.email_alerts || false}
                  onCheckedChange={(checked) => handleErrorLoggingChange('email_alerts', checked)}
                  disabled={loading.saving}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Maintenance Scheduling */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5" />
            System Maintenance Scheduling
          </CardTitle>
          <CardDescription>
            Schedule regular maintenance tasks and system updates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Enable Maintenance Mode</Label>
              <p className="text-sm text-muted-foreground">
                Temporarily disable public access during maintenance
              </p>
            </div>
            <Switch
              checked={settings.maintenance_settings?.maintenance_mode || false}
              onCheckedChange={(checked) => handleMaintenanceChange('maintenance_mode', checked)}
              disabled={loading.saving}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="maintenance-window">Preferred Maintenance Window</Label>
            <Select
              value={settings.maintenance_settings?.preferred_window || 'late_night'}
              onValueChange={(value) => handleMaintenanceChange('preferred_window', value)}
              disabled={loading.saving}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select maintenance window" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="early_morning">Early Morning (2-4 AM)</SelectItem>
                <SelectItem value="late_night">Late Night (11 PM - 1 AM)</SelectItem>
                <SelectItem value="weekend">Weekend (Saturday 2-4 AM)</SelectItem>
                <SelectItem value="custom">Custom Schedule</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Auto-update System Components</Label>
              <p className="text-sm text-muted-foreground">
                Automatically install security updates during maintenance
              </p>
            </div>
            <Switch
              checked={settings.maintenance_settings?.auto_update || false}
              onCheckedChange={(checked) => handleMaintenanceChange('auto_update', checked)}
              disabled={loading.saving}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
