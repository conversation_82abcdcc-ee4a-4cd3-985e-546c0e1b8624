'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Mail, 
  Plus, 
  X, 
  Upload, 
  Shield, 
  Edit, 
  User,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { createInvitation, bulkCreateInvitations } from '@/lib/database-helpers';
import type { UserRole } from '@/types';

interface UserInvitationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

const roleOptions: { value: UserRole; label: string; icon: React.ReactNode; description: string }[] = [
  { 
    value: 'admin', 
    label: 'Admin', 
    icon: <Shield className="h-4 w-4" />,
    description: 'Full access to all features and settings'
  },
  { 
    value: 'publisher', 
    label: 'Publisher', 
    icon: <Edit className="h-4 w-4" />,
    description: 'Can create, edit, and publish content'
  },
  { 
    value: 'editor', 
    label: 'Editor', 
    icon: <Edit className="h-4 w-4" />,
    description: 'Can create and edit content, but cannot publish'
  },
  { 
    value: 'member', 
    label: 'Member', 
    icon: <User className="h-4 w-4" />,
    description: 'Read-only access to content'
  },
];

export function UserInvitationDialog({
  open,
  onOpenChange,
  onSuccess,
}: UserInvitationDialogProps) {
  const [activeTab, setActiveTab] = useState('single');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Single invitation state
  const [email, setEmail] = useState('');
  const [role, setRole] = useState<UserRole>('member');
  const [message, setMessage] = useState('');

  // Bulk invitation state
  const [bulkEmails, setBulkEmails] = useState<string[]>([]);
  const [bulkRole, setBulkRole] = useState<UserRole>('member');
  const [bulkInput, setBulkInput] = useState('');

  const resetForm = () => {
    setEmail('');
    setRole('member');
    setMessage('');
    setBulkEmails([]);
    setBulkRole('member');
    setBulkInput('');
    setError(null);
    setSuccess(null);
  };

  const handleClose = () => {
    resetForm();
    onOpenChange(false);
  };

  const handleSingleInvitation = async () => {
    if (!email.trim()) {
      setError('Email is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await createInvitation(email.trim(), role);
      
      if (result.error) {
        setError(result.error);
        return;
      }

      setSuccess(`Invitation sent to ${email}`);
      setTimeout(() => {
        handleClose();
        onSuccess?.();
      }, 1500);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to send invitation');
    } finally {
      setLoading(false);
    }
  };

  const handleBulkInvitations = async () => {
    if (bulkEmails.length === 0) {
      setError('At least one email is required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const invitations = bulkEmails.map(email => ({ email, role: bulkRole }));
      const result = await bulkCreateInvitations(invitations);
      
      if (result.error && !result.data) {
        setError(result.error);
        return;
      }

      const successCount = result.data?.length || 0;
      const errorCount = result.errors?.length || 0;
      
      if (errorCount > 0) {
        setError(`${successCount} invitations sent, ${errorCount} failed`);
      } else {
        setSuccess(`${successCount} invitations sent successfully`);
      }

      setTimeout(() => {
        handleClose();
        onSuccess?.();
      }, 1500);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to send invitations');
    } finally {
      setLoading(false);
    }
  };

  const addBulkEmail = (emailToAdd: string) => {
    const email = emailToAdd.trim();
    if (email && !bulkEmails.includes(email)) {
      setBulkEmails([...bulkEmails, email]);
    }
  };

  const removeBulkEmail = (emailToRemove: string) => {
    setBulkEmails(bulkEmails.filter(email => email !== emailToRemove));
  };

  const handleBulkInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addBulkEmail(bulkInput);
      setBulkInput('');
    }
  };

  const parseBulkEmails = () => {
    const emails = bulkInput
      .split(/[,\n\r\t\s]+/)
      .map(email => email.trim())
      .filter(email => email && email.includes('@'));
    
    emails.forEach(addBulkEmail);
    setBulkInput('');
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Invite Users
          </DialogTitle>
          <DialogDescription>
            Send invitations to new users to join your organization
          </DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="single">Single Invitation</TabsTrigger>
            <TabsTrigger value="bulk">Bulk Invitations</TabsTrigger>
          </TabsList>

          <TabsContent value="single" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email">Email Address</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="role">Role</Label>
              <Select value={role} onValueChange={(value: UserRole) => setRole(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        {option.icon}
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="message">Custom Message (Optional)</Label>
              <Textarea
                id="message"
                placeholder="Add a personal message to the invitation..."
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                disabled={loading}
                rows={3}
              />
            </div>
          </TabsContent>

          <TabsContent value="bulk" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bulk-emails">Email Addresses</Label>
              <div className="space-y-2">
                <Input
                  id="bulk-emails"
                  placeholder="Enter emails separated by commas, spaces, or new lines"
                  value={bulkInput}
                  onChange={(e) => setBulkInput(e.target.value)}
                  onKeyDown={handleBulkInputKeyDown}
                  disabled={loading}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={parseBulkEmails}
                  disabled={!bulkInput.trim() || loading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Emails
                </Button>
              </div>
              
              {bulkEmails.length > 0 && (
                <div className="flex flex-wrap gap-2 p-2 border rounded-md bg-muted/50">
                  {bulkEmails.map((email, index) => (
                    <Badge key={index} variant="secondary" className="gap-1">
                      {email}
                      <button
                        onClick={() => removeBulkEmail(email)}
                        className="ml-1 hover:bg-muted rounded-full p-0.5"
                        disabled={loading}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="bulk-role">Role for All Users</Label>
              <Select value={bulkRole} onValueChange={(value: UserRole) => setBulkRole(value)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role" />
                </SelectTrigger>
                <SelectContent>
                  {roleOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        {option.icon}
                        <div>
                          <div className="font-medium">{option.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {option.description}
                          </div>
                        </div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </TabsContent>
        </Tabs>

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>{success}</AlertDescription>
          </Alert>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            onClick={activeTab === 'single' ? handleSingleInvitation : handleBulkInvitations}
            disabled={loading || (activeTab === 'single' ? !email.trim() : bulkEmails.length === 0)}
          >
            {loading ? 'Sending...' : `Send ${activeTab === 'single' ? 'Invitation' : 'Invitations'}`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
